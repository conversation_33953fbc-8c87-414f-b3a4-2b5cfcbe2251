# VG Kosh - Pinecone Integration Setup

This document explains how to set up and use the Pinecone integration for document upload and search functionality in your VG Kosh application.

## 🚀 Features

- **Web-based PDF Upload**: Upload PDFs through the dashboard interface
- **Bulk PDF Upload**: Python script for processing multiple PDFs at once
- **Semantic Search**: Search through document content using natural language
- **User Isolation**: Each user can only access their own documents
- **Chunk Management**: Automatic text splitting and embedding generation

## 📋 Prerequisites

1. **Pinecone Account**: Sign up at [https://app.pinecone.io/](https://app.pinecone.io/)
2. **OpenAI API Key**: Get from [https://platform.openai.com/api-keys](https://platform.openai.com/api-keys)
3. **Python 3.8+** (for bulk upload script)
4. **Node.js 18+** (for the web application)

## 🛠️ Setup Instructions

### 1. Create Pinecone Index

1. Log in to your Pinecone dashboard
2. Click "Create Index"
3. Configure the index:
   - **Name**: `vg-kosh-documents` (or your preferred name)
   - **Dimensions**: `1536` (for OpenAI text-embedding-ada-002)
   - **Metric**: `cosine`
   - **Pod Type**: `p1.x1` (starter tier)

### 2. Environment Variables

Copy `.env.example` to `.env.local` and fill in the required values:

```bash
# Pinecone Configuration
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_INDEX_NAME=vg-kosh-documents

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Other existing configurations...
```

### 3. Install Dependencies

#### Node.js Dependencies
```bash
npm install
```

#### Python Dependencies (for bulk upload)
```bash
pip install -r requirements.txt
```

### 4. Database Migration

Run the Supabase migration to create the documents table:

```bash
npx supabase migration up
```

Or manually run the SQL in `supabase/migrations/20241216000001_create_documents_table.sql`

## 📖 Usage

### Web Interface

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Access the Document Manager**:
   - Navigate to your dashboard
   - Look for the "Document Manager" section
   - Upload PDFs using the drag-and-drop interface
   - Search through uploaded documents

### Bulk Upload Script

For uploading multiple PDFs at once:

1. **Prepare your PDFs**:
   - Create a folder with your PDF files
   - Update the `PDF_FOLDER_PATH` in your `.env.local`

2. **Run the Python script**:
   ```bash
   cd src/scripts
   python upload_to_pinecone.py
   ```

3. **Monitor progress**:
   - The script shows upload progress
   - Supports resume functionality if interrupted
   - Processes files in batches to avoid rate limits

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PINECONE_API_KEY` | Your Pinecone API key | Required |
| `PINECONE_INDEX_NAME` | Name of your Pinecone index | `vg-kosh-documents` |
| `OPENAI_API_KEY` | Your OpenAI API key | Required |
| `PDF_FOLDER_PATH` | Path to PDF folder for bulk upload | `./uploads` |
| `START_BATCH` | Batch number to resume from | `0` |
| `BATCH_SIZE` | Number of chunks per batch | `50` |

### Text Chunking

- **Chunk Size**: 500 characters
- **Chunk Overlap**: 50 characters
- **Embedding Model**: `text-embedding-ada-002`

## 📡 API Endpoints

### Upload Document
```
POST /api/pinecone/upload
Content-Type: multipart/form-data

Body:
- file: PDF file
- title: Document title
- description: Document description (optional)
```

### Search Documents
```
POST /api/pinecone/search
Content-Type: application/json

Body:
{
  "query": "search query",
  "topK": 5,
  "documentIds": ["doc1", "doc2"] // optional
}
```

### Get User Documents
```
GET /api/pinecone/upload
```

## 🔍 Search Features

- **Semantic Search**: Find documents by meaning, not just keywords
- **Relevance Scoring**: Results ranked by similarity score
- **Document Filtering**: Search within specific documents
- **Context Preservation**: See which document and chunk contains the result

## 🛡️ Security

- **User Isolation**: RLS policies ensure users only access their documents
- **Authentication Required**: All endpoints require valid Supabase session
- **Metadata Filtering**: Pinecone queries filtered by user ID

## 🚨 Troubleshooting

### Common Issues

1. **"Index not found" error**:
   - Verify your Pinecone index name matches `PINECONE_INDEX_NAME`
   - Check that the index is created and active

2. **"Embedding failed" error**:
   - Verify your OpenAI API key is valid
   - Check your OpenAI account has sufficient credits

3. **"Upload failed" error**:
   - Ensure PDF file is valid and not corrupted
   - Check file size limits (adjust if needed)

4. **Python script issues**:
   - Verify all Python dependencies are installed
   - Check that the PDF folder path exists
   - Ensure environment variables are loaded

### Rate Limits

- **OpenAI**: 3,000 requests per minute (tier 1)
- **Pinecone**: Varies by plan (starter: 100 requests/minute)
- **Script includes delays**: Automatic rate limit handling

## 📈 Monitoring

### Check Index Statistics
```bash
# Using the web interface
GET /api/pinecone/stats

# Or check Pinecone dashboard
```

### Document Count
- View in the Document Manager interface
- Check Supabase `documents` table
- Monitor Pinecone index statistics

## 🔄 Maintenance

### Cleanup Old Documents
```sql
-- Delete documents older than 6 months
DELETE FROM documents 
WHERE created_at < NOW() - INTERVAL '6 months';
```

### Backup Considerations
- Supabase handles database backups
- Pinecone data should be backed up separately if critical
- Consider exporting document metadata regularly

## 📞 Support

For issues specific to:
- **Pinecone**: Check [Pinecone Documentation](https://docs.pinecone.io/)
- **OpenAI**: Check [OpenAI Documentation](https://platform.openai.com/docs)
- **Supabase**: Check [Supabase Documentation](https://supabase.com/docs)

## 🎯 Next Steps

1. **Test the integration** with a few sample PDFs
2. **Customize the UI** to match your brand
3. **Add more file types** (Word, TXT, etc.)
4. **Implement advanced search filters**
5. **Add document sharing features**
6. **Set up monitoring and analytics**
