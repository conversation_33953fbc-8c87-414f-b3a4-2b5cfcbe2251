# Supabase Configuration
# Public keys (safe to expose in browser)
NEXT_PUBLIC_SUPABASE_URL=https://xifuwecoszfaelnqvejx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU
POSTGRES_URL="****************************************/postgres"

# Service Role Key (server-side only, NEVER expose to browser)
# To find this key:
# 1. Go to https://supabase.com/dashboard
# 2. Select your project
# 3. Go to Project Settings > API
# 4. Copy the "service_role" key
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA2ODQ2OCwiZXhwIjoyMDY1NjQ0NDY4fQ.KqoYCpxXPOlDjYDZ87oZ4tsloqiz14FIbV8rFoKTStk

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Stripe Configuration
STRIPE_SECRET_KEY=
# To get the webhook secret:
# 1. Go to Stripe Dashboard > Developers > Webhooks
# 2. Click on your webhook endpoint
# 3. Click "Reveal" next to "Signing secret"
# 4. Copy that value here
STRIPE_WEBHOOK_SECRET=

NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=

OPENAI_API_KEY=

# Pinecone Configuration
# Get your API key from https://app.pinecone.io/
PINECONE_API_KEY=
PINECONE_INDEX_NAME=vg-kosh-documents

# Resend Configuration
# Get your API key from https://resend.com/dashboard/api-keys
RESEND_API_KEY=

# Python Script Configuration (for bulk PDF uploads)
PDF_FOLDER_PATH=./uploads
START_BATCH=0
BATCH_SIZE=50