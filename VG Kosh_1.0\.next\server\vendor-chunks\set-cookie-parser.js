"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/set-cookie-parser";
exports.ids = ["vendor-chunks/set-cookie-parser"];
exports.modules = {

/***/ "(ssr)/./node_modules/set-cookie-parser/lib/set-cookie.js":
/*!**********************************************************!*\
  !*** ./node_modules/set-cookie-parser/lib/set-cookie.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("\n\nvar defaultParseOptions = {\n  decodeValues: true,\n  map: false,\n  silent: false,\n};\n\nfunction isNonEmptyString(str) {\n  return typeof str === \"string\" && !!str.trim();\n}\n\nfunction parseString(setCookieValue, options) {\n  var parts = setCookieValue.split(\";\").filter(isNonEmptyString);\n\n  var nameValuePairStr = parts.shift();\n  var parsed = parseNameValuePair(nameValuePairStr);\n  var name = parsed.name;\n  var value = parsed.value;\n\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  try {\n    value = options.decodeValues ? decodeURIComponent(value) : value; // decode cookie value\n  } catch (e) {\n    console.error(\n      \"set-cookie-parser encountered an error while decoding a cookie with value '\" +\n        value +\n        \"'. Set options.decodeValues to false to disable this feature.\",\n      e\n    );\n  }\n\n  var cookie = {\n    name: name,\n    value: value,\n  };\n\n  parts.forEach(function (part) {\n    var sides = part.split(\"=\");\n    var key = sides.shift().trimLeft().toLowerCase();\n    var value = sides.join(\"=\");\n    if (key === \"expires\") {\n      cookie.expires = new Date(value);\n    } else if (key === \"max-age\") {\n      cookie.maxAge = parseInt(value, 10);\n    } else if (key === \"secure\") {\n      cookie.secure = true;\n    } else if (key === \"httponly\") {\n      cookie.httpOnly = true;\n    } else if (key === \"samesite\") {\n      cookie.sameSite = value;\n    } else if (key === \"partitioned\") {\n      cookie.partitioned = true;\n    } else {\n      cookie[key] = value;\n    }\n  });\n\n  return cookie;\n}\n\nfunction parseNameValuePair(nameValuePairStr) {\n  // Parses name-value-pair according to rfc6265bis draft\n\n  var name = \"\";\n  var value = \"\";\n  var nameValueArr = nameValuePairStr.split(\"=\");\n  if (nameValueArr.length > 1) {\n    name = nameValueArr.shift();\n    value = nameValueArr.join(\"=\"); // everything after the first =, joined by a \"=\" if there was more than one part\n  } else {\n    value = nameValuePairStr;\n  }\n\n  return { name: name, value: value };\n}\n\nfunction parse(input, options) {\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  if (!input) {\n    if (!options.map) {\n      return [];\n    } else {\n      return {};\n    }\n  }\n\n  if (input.headers) {\n    if (typeof input.headers.getSetCookie === \"function\") {\n      // for fetch responses - they combine headers of the same type in the headers array,\n      // but getSetCookie returns an uncombined array\n      input = input.headers.getSetCookie();\n    } else if (input.headers[\"set-cookie\"]) {\n      // fast-path for node.js (which automatically normalizes header names to lower-case\n      input = input.headers[\"set-cookie\"];\n    } else {\n      // slow-path for other environments - see #25\n      var sch =\n        input.headers[\n          Object.keys(input.headers).find(function (key) {\n            return key.toLowerCase() === \"set-cookie\";\n          })\n        ];\n      // warn if called on a request-like object with a cookie header rather than a set-cookie header - see #34, 36\n      if (!sch && input.headers.cookie && !options.silent) {\n        console.warn(\n          \"Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning.\"\n        );\n      }\n      input = sch;\n    }\n  }\n  if (!Array.isArray(input)) {\n    input = [input];\n  }\n\n  if (!options.map) {\n    return input.filter(isNonEmptyString).map(function (str) {\n      return parseString(str, options);\n    });\n  } else {\n    var cookies = {};\n    return input.filter(isNonEmptyString).reduce(function (cookies, str) {\n      var cookie = parseString(str, options);\n      cookies[cookie.name] = cookie;\n      return cookies;\n    }, cookies);\n  }\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n\n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nfunction splitCookiesString(cookiesString) {\n  if (Array.isArray(cookiesString)) {\n    return cookiesString;\n  }\n  if (typeof cookiesString !== \"string\") {\n    return [];\n  }\n\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos;\n        pos += 1;\n\n        skipWhitespace();\n        nextStart = pos;\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          // we found cookies separator\n          cookiesSeparatorFound = true;\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n\n  return cookiesStrings;\n}\n\nmodule.exports = parse;\nmodule.exports.parse = parse;\nmodule.exports.parseString = parseString;\nmodule.exports.splitCookiesString = splitCookiesString;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/set-cookie-parser/lib/set-cookie.js\n");

/***/ })

};
;