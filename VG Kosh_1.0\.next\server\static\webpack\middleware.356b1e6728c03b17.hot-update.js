"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/@supabase/auth-helpers-nextjs/dist/index.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  createBrowserSupabaseClient: () => createBrowserSupabaseClient,\n  createClientComponentClient: () => createClientComponentClient,\n  createMiddlewareClient: () => createMiddlewareClient,\n  createMiddlewareSupabaseClient: () => createMiddlewareSupabaseClient,\n  createPagesBrowserClient: () => createPagesBrowserClient,\n  createPagesServerClient: () => createPagesServerClient,\n  createRouteHandlerClient: () => createRouteHandlerClient,\n  createServerActionClient: () => createServerActionClient,\n  createServerComponentClient: () => createServerComponentClient,\n  createServerSupabaseClient: () => createServerSupabaseClient\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/clientComponentClient.ts\nvar import_auth_helpers_shared = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(middleware)/./node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar supabase;\nfunction createClientComponentClient({\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions,\n  isSingleton = true\n} = {}) {\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      \"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\"\n    );\n  }\n  const createNewClient = () => {\n    var _a;\n    return (0, import_auth_helpers_shared.createSupabaseClient)(supabaseUrl, supabaseKey, {\n      ...options,\n      global: {\n        ...options == null ? void 0 : options.global,\n        headers: {\n          ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n          \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.10.0\"}`\n        }\n      },\n      auth: {\n        storage: new import_auth_helpers_shared.BrowserCookieAuthStorageAdapter(cookieOptions)\n      }\n    });\n  };\n  if (isSingleton) {\n    const _supabase = supabase ?? createNewClient();\n    if (typeof window === \"undefined\")\n      return _supabase;\n    if (!supabase)\n      supabase = _supabase;\n    return supabase;\n  }\n  return createNewClient();\n}\n\n// src/pagesBrowserClient.ts\nvar createPagesBrowserClient = createClientComponentClient;\n\n// src/pagesServerClient.ts\nvar import_auth_helpers_shared2 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(middleware)/./node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar import_set_cookie_parser = __webpack_require__(/*! set-cookie-parser */ \"(middleware)/./node_modules/set-cookie-parser/lib/set-cookie.js\");\nvar NextServerAuthStorageAdapter = class extends import_auth_helpers_shared2.CookieAuthStorageAdapter {\n  constructor(context, cookieOptions) {\n    super(cookieOptions);\n    this.context = context;\n  }\n  getCookie(name) {\n    var _a, _b, _c;\n    const setCookie = (0, import_set_cookie_parser.splitCookiesString)(\n      ((_b = (_a = this.context.res) == null ? void 0 : _a.getHeader(\"set-cookie\")) == null ? void 0 : _b.toString()) ?? \"\"\n    ).map((c) => (0, import_auth_helpers_shared2.parseCookies)(c)[name]).find((c) => !!c);\n    const value = setCookie ?? ((_c = this.context.req) == null ? void 0 : _c.cookies[name]);\n    return value;\n  }\n  setCookie(name, value) {\n    this._setCookie(name, value);\n  }\n  deleteCookie(name) {\n    this._setCookie(name, \"\", {\n      maxAge: 0\n    });\n  }\n  _setCookie(name, value, options) {\n    var _a;\n    const setCookies = (0, import_set_cookie_parser.splitCookiesString)(\n      ((_a = this.context.res.getHeader(\"set-cookie\")) == null ? void 0 : _a.toString()) ?? \"\"\n    ).filter((c) => !(name in (0, import_auth_helpers_shared2.parseCookies)(c)));\n    const cookieStr = (0, import_auth_helpers_shared2.serializeCookie)(name, value, {\n      ...this.cookieOptions,\n      ...options,\n      // Allow supabase-js on the client to read the cookie as well\n      httpOnly: false\n    });\n    this.context.res.setHeader(\"set-cookie\", [...setCookies, cookieStr]);\n  }\n};\nfunction createPagesServerClient(context, {\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions\n} = {}) {\n  var _a;\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      \"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\"\n    );\n  }\n  return (0, import_auth_helpers_shared2.createSupabaseClient)(supabaseUrl, supabaseKey, {\n    ...options,\n    global: {\n      ...options == null ? void 0 : options.global,\n      headers: {\n        ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n        \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.10.0\"}`\n      }\n    },\n    auth: {\n      storage: new NextServerAuthStorageAdapter(context, cookieOptions)\n    }\n  });\n}\n\n// src/middlewareClient.ts\nvar import_auth_helpers_shared3 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(middleware)/./node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar import_set_cookie_parser2 = __webpack_require__(/*! set-cookie-parser */ \"(middleware)/./node_modules/set-cookie-parser/lib/set-cookie.js\");\nvar NextMiddlewareAuthStorageAdapter = class extends import_auth_helpers_shared3.CookieAuthStorageAdapter {\n  constructor(context, cookieOptions) {\n    super(cookieOptions);\n    this.context = context;\n  }\n  getCookie(name) {\n    var _a;\n    const setCookie = (0, import_set_cookie_parser2.splitCookiesString)(\n      ((_a = this.context.res.headers.get(\"set-cookie\")) == null ? void 0 : _a.toString()) ?? \"\"\n    ).map((c) => (0, import_auth_helpers_shared3.parseCookies)(c)[name]).find((c) => !!c);\n    if (setCookie) {\n      return setCookie;\n    }\n    const cookies = (0, import_auth_helpers_shared3.parseCookies)(this.context.req.headers.get(\"cookie\") ?? \"\");\n    return cookies[name];\n  }\n  setCookie(name, value) {\n    this._setCookie(name, value);\n  }\n  deleteCookie(name) {\n    this._setCookie(name, \"\", {\n      maxAge: 0\n    });\n  }\n  _setCookie(name, value, options) {\n    const newSessionStr = (0, import_auth_helpers_shared3.serializeCookie)(name, value, {\n      ...this.cookieOptions,\n      ...options,\n      // Allow supabase-js on the client to read the cookie as well\n      httpOnly: false\n    });\n    if (this.context.res.headers) {\n      this.context.res.headers.append(\"set-cookie\", newSessionStr);\n    }\n  }\n};\nfunction createMiddlewareClient(context, {\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions\n} = {}) {\n  var _a;\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      \"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\"\n    );\n  }\n  return (0, import_auth_helpers_shared3.createSupabaseClient)(supabaseUrl, supabaseKey, {\n    ...options,\n    global: {\n      ...options == null ? void 0 : options.global,\n      headers: {\n        ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n        \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.10.0\"}`\n      }\n    },\n    auth: {\n      storage: new NextMiddlewareAuthStorageAdapter(context, cookieOptions)\n    }\n  });\n}\n\n// src/serverComponentClient.ts\nvar import_auth_helpers_shared4 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(middleware)/./node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar NextServerComponentAuthStorageAdapter = class extends import_auth_helpers_shared4.CookieAuthStorageAdapter {\n  constructor(context, cookieOptions) {\n    super(cookieOptions);\n    this.context = context;\n    this.isServer = true;\n  }\n  getCookie(name) {\n    var _a;\n    const nextCookies = this.context.cookies();\n    return (_a = nextCookies.get(name)) == null ? void 0 : _a.value;\n  }\n  setCookie(name, value) {\n  }\n  deleteCookie(name) {\n  }\n};\nfunction createServerComponentClient(context, {\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions\n} = {}) {\n  var _a;\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      \"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\"\n    );\n  }\n  return (0, import_auth_helpers_shared4.createSupabaseClient)(supabaseUrl, supabaseKey, {\n    ...options,\n    global: {\n      ...options == null ? void 0 : options.global,\n      headers: {\n        ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n        \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.10.0\"}`\n      }\n    },\n    auth: {\n      storage: new NextServerComponentAuthStorageAdapter(context, cookieOptions)\n    }\n  });\n}\n\n// src/routeHandlerClient.ts\nvar import_auth_helpers_shared5 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(middleware)/./node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar NextRouteHandlerAuthStorageAdapter = class extends import_auth_helpers_shared5.CookieAuthStorageAdapter {\n  constructor(context, cookieOptions) {\n    super(cookieOptions);\n    this.context = context;\n  }\n  getCookie(name) {\n    var _a;\n    const nextCookies = this.context.cookies();\n    return (_a = nextCookies.get(name)) == null ? void 0 : _a.value;\n  }\n  setCookie(name, value) {\n    const nextCookies = this.context.cookies();\n    nextCookies.set(name, value, this.cookieOptions);\n  }\n  deleteCookie(name) {\n    const nextCookies = this.context.cookies();\n    nextCookies.set(name, \"\", {\n      ...this.cookieOptions,\n      maxAge: 0\n    });\n  }\n};\nfunction createRouteHandlerClient(context, {\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions\n} = {}) {\n  var _a;\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      \"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\"\n    );\n  }\n  return (0, import_auth_helpers_shared5.createSupabaseClient)(supabaseUrl, supabaseKey, {\n    ...options,\n    global: {\n      ...options == null ? void 0 : options.global,\n      headers: {\n        ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n        \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.10.0\"}`\n      }\n    },\n    auth: {\n      storage: new NextRouteHandlerAuthStorageAdapter(context, cookieOptions)\n    }\n  });\n}\n\n// src/serverActionClient.ts\nvar createServerActionClient = createRouteHandlerClient;\n\n// src/deprecated.ts\nfunction createBrowserSupabaseClient({\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions\n} = {}) {\n  console.warn(\n    \"Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages\"\n  );\n  return createPagesBrowserClient({\n    supabaseUrl,\n    supabaseKey,\n    options,\n    cookieOptions\n  });\n}\nfunction createServerSupabaseClient(context, {\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions\n} = {}) {\n  console.warn(\n    \"Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages\"\n  );\n  return createPagesServerClient(context, {\n    supabaseUrl,\n    supabaseKey,\n    options,\n    cookieOptions\n  });\n}\nfunction createMiddlewareSupabaseClient(context, {\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions\n} = {}) {\n  console.warn(\n    \"Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware\"\n  );\n  return createMiddlewareClient(context, {\n    supabaseUrl,\n    supabaseKey,\n    options,\n    cookieOptions\n  });\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\n");

/***/ })

});