{"name": "best-saas-kit-pro", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "2.0.18", "@react-email/components": "^0.0.31", "@react-email/render": "^1.0.3", "@stripe/stripe-js": "^5.2.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.47.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "next": "^15.1.0", "openai": "^4.76.1", "react": "18.2.0", "react-dom": "18.2.0", "react-email": "^3.0.4", "recharts": "^2.12.0", "resend": "^4.0.1", "stripe": "^17.4.0", "tailwind-merge": "^2.5.5"}, "devDependencies": {"@types/node": "20.10.0", "@types/react": "18.2.39", "@types/react-dom": "18.2.17", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "autoprefixer": "10.4.16", "eslint": "^8.57.0", "eslint-config-next": "^15.1.0", "postcss": "8.4.31", "tailwindcss": "3.3.5", "typescript": "5.3.2"}}