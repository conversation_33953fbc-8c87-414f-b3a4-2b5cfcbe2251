/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1ByaXlhZGFyc2hhbiUyMFRpd2FyaSU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1ZHJTIwS29zaF8xLjAlNUMlNUNWRyUyMEtvc2hfMS4wJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUF1SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUHJpeWFkYXJzaGFuIFRpd2FyaVxcXFxEb3dubG9hZHNcXFxcVkcgS29zaF8xLjBcXFxcVkcgS29zaF8xLjBcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1ByaXlhZGFyc2hhbiUyMFRpd2FyaSU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1ZHJTIwS29zaF8xLjAlNUMlNUNWRyUyMEtvc2hfMS4wJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUF1SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUHJpeWFkYXJzaGFuIFRpd2FyaVxcXFxEb3dubG9hZHNcXFxcVkcgS29zaF8xLjBcXFxcVkcgS29zaF8xLjBcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/components/FaqItem.tsx":
/*!************************************!*\
  !*** ./app/components/FaqItem.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/ChevronDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst FaqItem = ({ question, answer })=>{\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleToggle = ()=>setIsOpen(!isOpen);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-b border-gray-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"w-full py-6 flex justify-between items-center text-left\",\n                onClick: handleToggle,\n                \"aria-expanded\": isOpen,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg font-medium\",\n                        children: question\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\FaqItem.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: `w-5 h-5 transition-transform ${isOpen ? 'transform rotate-180' : ''}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\FaqItem.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\FaqItem.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `overflow-hidden transition-all duration-300 ${isOpen ? 'max-h-96 pb-6' : 'max-h-0'}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400\",\n                    children: answer\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\FaqItem.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\FaqItem.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\FaqItem.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FaqItem);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/FaqItem.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/PricingCard.tsx":
/*!****************************************!*\
  !*** ./app/components/PricingCard.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/CheckIcon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _stripe_stripe_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @stripe/stripe-js */ \"(ssr)/./node_modules/@stripe/stripe-js/lib/index.mjs\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Initialize Stripe\nconst stripePromise = (0,_stripe_stripe_js__WEBPACK_IMPORTED_MODULE_3__.loadStripe)(\"\");\nconst PricingCard = ({ title, price, description, features, buttonText, popular = false, priceId })=>{\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_4__.createClientComponentClient)();\n    const handleSubscribe = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            // Check if user is authenticated\n            const { data: { session } } = await supabase.auth.getSession();\n            if (!session) {\n                // If not authenticated, redirect to login with return URL\n                const returnUrl = '/dashboard/billing';\n                router.push(`/auth?returnUrl=${encodeURIComponent(returnUrl)}`);\n                return;\n            }\n            // Create Stripe Checkout Session\n            const response = await fetch('/api/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    priceId\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout URL\n            window.location.href = result.url;\n        } catch (err) {\n            console.error('Error:', err);\n            setError(err instanceof Error ? err.message : 'Failed to process subscription');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-lg p-8 ${popular ? 'bg-green-900/20 border-2 border-green-500' : 'bg-gray-800/50 border border-gray-700'}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-xl font-semibold mb-4\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\PricingCard.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-4xl font-bold\",\n                        children: [\n                            \"$\",\n                            price\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\PricingCard.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400 ml-2\",\n                        children: \"/month\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\PricingCard.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\PricingCard.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-400 mb-6\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\PricingCard.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"space-y-3 mb-8\",\n                children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"flex items-start gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-5 h-5 text-green-500 flex-shrink-0 mt-0.5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\PricingCard.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-300\",\n                                children: feature\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\PricingCard.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\PricingCard.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\PricingCard.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-red-500 text-sm mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\PricingCard.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleSubscribe,\n                disabled: isLoading,\n                className: `w-full text-center py-3 px-6 rounded-lg transition-colors ${popular ? 'bg-green-500 hover:bg-green-600 text-black' : 'bg-white hover:bg-gray-200 text-black'} disabled:opacity-50`,\n                children: isLoading ? 'Loading...' : buttonText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\PricingCard.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\components\\\\PricingCard.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PricingCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9QcmljaW5nQ2FyZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFFcUQ7QUFDckI7QUFDVztBQUNHO0FBQzZCO0FBRTNFLG9CQUFvQjtBQUNwQixNQUFNSyxnQkFBZ0JGLDZEQUFVQSxDQUFDRyxFQUE4QztBQVkvRSxNQUFNRyxjQUFjLENBQUMsRUFDbkJDLEtBQUssRUFDTEMsS0FBSyxFQUNMQyxXQUFXLEVBQ1hDLFFBQVEsRUFDUkMsVUFBVSxFQUNWQyxVQUFVLEtBQUssRUFDZkMsT0FBTyxFQUNVO0lBQ2pCLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHakIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDa0IsT0FBT0MsU0FBUyxHQUFHbkIsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU1vQixTQUFTbkIsMERBQVNBO0lBQ3hCLE1BQU1vQixXQUFXbEIsMEZBQTJCQTtJQUU1QyxNQUFNbUIsa0JBQWtCO1FBQ3RCLElBQUk7WUFDRkwsYUFBYTtZQUNiRSxTQUFTO1lBRVQsaUNBQWlDO1lBQ2pDLE1BQU0sRUFBRUksTUFBTSxFQUFFQyxPQUFPLEVBQUUsRUFBRSxHQUFHLE1BQU1ILFNBQVNJLElBQUksQ0FBQ0MsVUFBVTtZQUU1RCxJQUFJLENBQUNGLFNBQVM7Z0JBQ1osMERBQTBEO2dCQUMxRCxNQUFNRyxZQUFZO2dCQUNsQlAsT0FBT1EsSUFBSSxDQUFDLENBQUMsZ0JBQWdCLEVBQUVDLG1CQUFtQkYsWUFBWTtnQkFDOUQ7WUFDRjtZQUVBLGlDQUFpQztZQUNqQyxNQUFNRyxXQUFXLE1BQU1DLE1BQU0sZ0NBQWdDO2dCQUMzREMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CckI7Z0JBQ0Y7WUFDRjtZQUVBLE1BQU1zQixTQUFTLE1BQU1QLFNBQVNRLElBQUk7WUFFbEMsSUFBSSxDQUFDUixTQUFTUyxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTUgsT0FBT25CLEtBQUssSUFBSTtZQUNsQztZQUVBLGtDQUFrQztZQUNsQ3VCLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHTixPQUFPTyxHQUFHO1FBQ25DLEVBQUUsT0FBT0MsS0FBSztZQUNaQyxRQUFRNUIsS0FBSyxDQUFDLFVBQVUyQjtZQUN4QjFCLFNBQVMwQixlQUFlTCxRQUFRSyxJQUFJRSxPQUFPLEdBQUc7UUFDaEQsU0FBVTtZQUNSOUIsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQytCO1FBQ0NDLFdBQVcsQ0FBQyxlQUFlLEVBQ3pCbkMsVUFDSSw4Q0FDQSx5Q0FDSjs7MEJBRUYsOERBQUNvQztnQkFBR0QsV0FBVTswQkFBOEJ4Qzs7Ozs7OzBCQUM1Qyw4REFBQ3VDO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0U7d0JBQUtGLFdBQVU7OzRCQUFxQjs0QkFBRXZDOzs7Ozs7O2tDQUN2Qyw4REFBQ3lDO3dCQUFLRixXQUFVO2tDQUFxQjs7Ozs7Ozs7Ozs7OzBCQUV2Qyw4REFBQ0c7Z0JBQUVILFdBQVU7MEJBQXNCdEM7Ozs7OzswQkFDbkMsOERBQUMwQztnQkFBR0osV0FBVTswQkFDWHJDLFNBQVMwQyxHQUFHLENBQUMsQ0FBQ0MsU0FBU0Msc0JBQ3RCLDhEQUFDQzt3QkFBZVIsV0FBVTs7MENBQ3hCLDhEQUFDbEQsaUdBQVNBO2dDQUFDa0QsV0FBVTs7Ozs7OzBDQUNyQiw4REFBQ0U7Z0NBQUtGLFdBQVU7MENBQXlCTTs7Ozs7Ozt1QkFGbENDOzs7Ozs7Ozs7O1lBTVp0Qyx1QkFDQyw4REFBQzhCO2dCQUFJQyxXQUFVOzBCQUNaL0I7Ozs7OzswQkFHTCw4REFBQ3dDO2dCQUNDQyxTQUFTckM7Z0JBQ1RzQyxVQUFVNUM7Z0JBQ1ZpQyxXQUFXLENBQUMsMERBQTBELEVBQ3BFbkMsVUFDSSwrQ0FDQSx3Q0FDTCxvQkFBb0IsQ0FBQzswQkFFckJFLFlBQVksZUFBZUg7Ozs7Ozs7Ozs7OztBQUlwQztBQUVBLGlFQUFlTCxXQUFXQSxFQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFByaXlhZGFyc2hhbiBUaXdhcmlcXERvd25sb2Fkc1xcVkcgS29zaF8xLjBcXFZHIEtvc2hfMS4wXFxhcHBcXGNvbXBvbmVudHNcXFByaWNpbmdDYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCB7IENoZWNrSWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvc29saWQnXHJcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXHJcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcclxuaW1wb3J0IHsgbG9hZFN0cmlwZSB9IGZyb20gJ0BzdHJpcGUvc3RyaXBlLWpzJ1xyXG5pbXBvcnQgeyBjcmVhdGVDbGllbnRDb21wb25lbnRDbGllbnQgfSBmcm9tICdAc3VwYWJhc2UvYXV0aC1oZWxwZXJzLW5leHRqcydcclxuXHJcbi8vIEluaXRpYWxpemUgU3RyaXBlXHJcbmNvbnN0IHN0cmlwZVByb21pc2UgPSBsb2FkU3RyaXBlKHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NUUklQRV9QVUJMSVNIQUJMRV9LRVkhKVxyXG5cclxuaW50ZXJmYWNlIFByaWNpbmdDYXJkUHJvcHMge1xyXG4gIHRpdGxlOiBzdHJpbmdcclxuICBwcmljZTogc3RyaW5nXHJcbiAgZGVzY3JpcHRpb246IHN0cmluZ1xyXG4gIGZlYXR1cmVzOiBzdHJpbmdbXVxyXG4gIGJ1dHRvblRleHQ6IHN0cmluZ1xyXG4gIHBvcHVsYXI/OiBib29sZWFuXHJcbiAgcHJpY2VJZDogc3RyaW5nXHJcbn1cclxuXHJcbmNvbnN0IFByaWNpbmdDYXJkID0gKHtcclxuICB0aXRsZSxcclxuICBwcmljZSxcclxuICBkZXNjcmlwdGlvbixcclxuICBmZWF0dXJlcyxcclxuICBidXR0b25UZXh0LFxyXG4gIHBvcHVsYXIgPSBmYWxzZSxcclxuICBwcmljZUlkLFxyXG59OiBQcmljaW5nQ2FyZFByb3BzKSA9PiB7XHJcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxyXG4gIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50Q29tcG9uZW50Q2xpZW50KClcclxuXHJcbiAgY29uc3QgaGFuZGxlU3Vic2NyaWJlID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgc2V0SXNMb2FkaW5nKHRydWUpXHJcbiAgICAgIHNldEVycm9yKG51bGwpXHJcblxyXG4gICAgICAvLyBDaGVjayBpZiB1c2VyIGlzIGF1dGhlbnRpY2F0ZWRcclxuICAgICAgY29uc3QgeyBkYXRhOiB7IHNlc3Npb24gfSB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRTZXNzaW9uKClcclxuICAgICAgXHJcbiAgICAgIGlmICghc2Vzc2lvbikge1xyXG4gICAgICAgIC8vIElmIG5vdCBhdXRoZW50aWNhdGVkLCByZWRpcmVjdCB0byBsb2dpbiB3aXRoIHJldHVybiBVUkxcclxuICAgICAgICBjb25zdCByZXR1cm5VcmwgPSAnL2Rhc2hib2FyZC9iaWxsaW5nJ1xyXG4gICAgICAgIHJvdXRlci5wdXNoKGAvYXV0aD9yZXR1cm5Vcmw9JHtlbmNvZGVVUklDb21wb25lbnQocmV0dXJuVXJsKX1gKVxyXG4gICAgICAgIHJldHVyblxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBDcmVhdGUgU3RyaXBlIENoZWNrb3V0IFNlc3Npb25cclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9jcmVhdGUtY2hlY2tvdXQtc2Vzc2lvbicsIHtcclxuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgcHJpY2VJZCxcclxuICAgICAgICB9KSxcclxuICAgICAgfSlcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxyXG4gICAgICBcclxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihyZXN1bHQuZXJyb3IgfHwgJ0ZhaWxlZCB0byBjcmVhdGUgY2hlY2tvdXQgc2Vzc2lvbicpXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFJlZGlyZWN0IHRvIFN0cmlwZSBDaGVja291dCBVUkxcclxuICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSByZXN1bHQudXJsXHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3I6JywgZXJyKVxyXG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0ZhaWxlZCB0byBwcm9jZXNzIHN1YnNjcmlwdGlvbicpXHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdlxyXG4gICAgICBjbGFzc05hbWU9e2Byb3VuZGVkLWxnIHAtOCAke1xyXG4gICAgICAgIHBvcHVsYXJcclxuICAgICAgICAgID8gJ2JnLWdyZWVuLTkwMC8yMCBib3JkZXItMiBib3JkZXItZ3JlZW4tNTAwJ1xyXG4gICAgICAgICAgOiAnYmctZ3JheS04MDAvNTAgYm9yZGVyIGJvcmRlci1ncmF5LTcwMCdcclxuICAgICAgfWB9XHJcbiAgICA+XHJcbiAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgbWItNFwiPnt0aXRsZX08L2gzPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cclxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGRcIj4ke3ByaWNlfTwvc3Bhbj5cclxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIG1sLTJcIj4vbW9udGg8L3NwYW4+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIG1iLTZcIj57ZGVzY3JpcHRpb259PC9wPlxyXG4gICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0zIG1iLThcIj5cclxuICAgICAgICB7ZmVhdHVyZXMubWFwKChmZWF0dXJlLCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgPGxpIGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgZ2FwLTNcIj5cclxuICAgICAgICAgICAgPENoZWNrSWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtZ3JlZW4tNTAwIGZsZXgtc2hyaW5rLTAgbXQtMC41XCIgLz5cclxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktMzAwXCI+e2ZlYXR1cmV9PC9zcGFuPlxyXG4gICAgICAgICAgPC9saT5cclxuICAgICAgICApKX1cclxuICAgICAgPC91bD5cclxuICAgICAge2Vycm9yICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCB0ZXh0LXNtIG1iLTRcIj5cclxuICAgICAgICAgIHtlcnJvcn1cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKX1cclxuICAgICAgPGJ1dHRvblxyXG4gICAgICAgIG9uQ2xpY2s9e2hhbmRsZVN1YnNjcmliZX1cclxuICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxyXG4gICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCB0ZXh0LWNlbnRlciBweS0zIHB4LTYgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyAke1xyXG4gICAgICAgICAgcG9wdWxhclxyXG4gICAgICAgICAgICA/ICdiZy1ncmVlbi01MDAgaG92ZXI6YmctZ3JlZW4tNjAwIHRleHQtYmxhY2snXHJcbiAgICAgICAgICAgIDogJ2JnLXdoaXRlIGhvdmVyOmJnLWdyYXktMjAwIHRleHQtYmxhY2snXHJcbiAgICAgICAgfSBkaXNhYmxlZDpvcGFjaXR5LTUwYH1cclxuICAgICAgPlxyXG4gICAgICAgIHtpc0xvYWRpbmcgPyAnTG9hZGluZy4uLicgOiBidXR0b25UZXh0fVxyXG4gICAgICA8L2J1dHRvbj5cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgUHJpY2luZ0NhcmQgIl0sIm5hbWVzIjpbIkNoZWNrSWNvbiIsInVzZVN0YXRlIiwidXNlUm91dGVyIiwibG9hZFN0cmlwZSIsImNyZWF0ZUNsaWVudENvbXBvbmVudENsaWVudCIsInN0cmlwZVByb21pc2UiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1RSSVBFX1BVQkxJU0hBQkxFX0tFWSIsIlByaWNpbmdDYXJkIiwidGl0bGUiLCJwcmljZSIsImRlc2NyaXB0aW9uIiwiZmVhdHVyZXMiLCJidXR0b25UZXh0IiwicG9wdWxhciIsInByaWNlSWQiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwicm91dGVyIiwic3VwYWJhc2UiLCJoYW5kbGVTdWJzY3JpYmUiLCJkYXRhIiwic2Vzc2lvbiIsImF1dGgiLCJnZXRTZXNzaW9uIiwicmV0dXJuVXJsIiwicHVzaCIsImVuY29kZVVSSUNvbXBvbmVudCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJyZXN1bHQiLCJqc29uIiwib2siLCJFcnJvciIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsInVybCIsImVyciIsImNvbnNvbGUiLCJtZXNzYWdlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDMiLCJzcGFuIiwicCIsInVsIiwibWFwIiwiZmVhdHVyZSIsImluZGV4IiwibGkiLCJidXR0b24iLCJvbkNsaWNrIiwiZGlzYWJsZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/PricingCard.tsx\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpenIcon,CodeBracketIcon,CpuChipIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpenIcon,CodeBracketIcon,CpuChipIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpenIcon,CodeBracketIcon,CpuChipIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpenIcon,CodeBracketIcon,CpuChipIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BookOpenIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpenIcon,CodeBracketIcon,CpuChipIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpenIcon,CodeBracketIcon,CpuChipIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpenIcon,CodeBracketIcon,CpuChipIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _components_PricingCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/PricingCard */ \"(ssr)/./app/components/PricingCard.tsx\");\n/* harmony import */ var _components_FaqItem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/FaqItem */ \"(ssr)/./app/components/FaqItem.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst features = [\n    {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"w-6 h-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n            lineNumber: 21,\n            columnNumber: 11\n        }, undefined),\n        title: 'Production-Ready Code',\n        description: 'Get started with professionally written, well-structured code that scales.'\n    },\n    {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"w-6 h-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n            lineNumber: 26,\n            columnNumber: 11\n        }, undefined),\n        title: 'Quick Deployment',\n        description: 'Deploy your application with one click to your favorite cloud platform.'\n    },\n    {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"w-6 h-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n            lineNumber: 31,\n            columnNumber: 11\n        }, undefined),\n        title: 'AI Integration',\n        description: 'Built-in AI capabilities to supercharge your application features.'\n    }\n];\nconst pricingPlans = [\n    {\n        title: 'Starter',\n        price: '99',\n        description: 'Perfect for side projects and small startups',\n        features: [\n            'Up to 5 team members',\n            'Basic analytics',\n            'Community support',\n            '5GB storage',\n            'API access'\n        ],\n        buttonText: 'Get Started',\n        priceId: 'price_1QTPalGI6vk81n8V8PtyW1ow'\n    },\n    {\n        title: 'Pro',\n        price: '249',\n        description: 'Best for growing businesses',\n        features: [\n            'Unlimited team members',\n            'Advanced analytics',\n            'Priority support',\n            '50GB storage',\n            'API access',\n            'Custom integrations'\n        ],\n        buttonText: 'Start Free Trial',\n        popular: true,\n        priceId: 'price_1QTPbgGI6vk81n8VgYFOi983'\n    },\n    {\n        title: 'Enterprise',\n        price: '999',\n        description: 'For large scale applications',\n        features: [\n            'Unlimited everything',\n            'White-label options',\n            '24/7 phone support',\n            '500GB storage',\n            'API access',\n            'Custom development'\n        ],\n        buttonText: 'Contact Sales',\n        priceId: 'price_1QTPcUGI6vk81n8V9567pzL9'\n    }\n];\nconst faqs = [\n    {\n        question: 'What is included in the starter package?',\n        answer: 'The starter package includes all essential features to get your project up and running, including basic analytics, community support, and API access.'\n    },\n    {\n        question: 'Can I upgrade my plan later?',\n        answer: 'Yes, you can upgrade your plan at any time. Your new features will be available immediately after upgrading.'\n    },\n    {\n        question: 'Do you offer custom development?',\n        answer: 'Yes, our enterprise plan includes custom development options to meet your specific needs.'\n    }\n];\nconst testimonials = [\n    {\n        content: \"This toolkit saved us months of development time. We launched our MVP in just 2 weeks!\",\n        author: {\n            name: \"Sarah Chen\",\n            avatar: \"/avatars/placeholder.svg\",\n            title: \"CTO at TechStart\"\n        },\n        stats: [\n            {\n                label: \"Time Saved\",\n                value: \"3 months\"\n            },\n            {\n                label: \"ROI\",\n                value: \"300%\"\n            }\n        ]\n    },\n    {\n        content: \"The code quality is exceptional. It's like having a senior developer on the team.\",\n        author: {\n            name: \"Mike Johnson\",\n            avatar: \"/avatars/placeholder.svg\",\n            title: \"Lead Developer\"\n        }\n    },\n    {\n        content: \"Best investment we made for our startup. The support is amazing too!\",\n        author: {\n            name: \"Lisa Park\",\n            avatar: \"/avatars/placeholder.svg\",\n            title: \"Founder at AppLabs\"\n        }\n    }\n];\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#0A0A0A]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full z-50 bg-[#0A0A0A]/80 backdrop-blur-sm border-b border-white/5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"text-xl font-bold text-white\",\n                                    children: \"Startup\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/features\",\n                                        className: \"text-sm text-white/70 hover:text-white\",\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/pricing\",\n                                        className: \"text-sm text-white/70 hover:text-white\",\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/docs\",\n                                        className: \"text-sm text-white/70 hover:text-white flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Docs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"https://github.com/zainulabedeen123/best-saas-kit/discussions\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"text-sm text-white/70 hover:text-white\",\n                                        children: \"Community\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/blog\",\n                                        className: \"text-sm text-white/70 hover:text-white\",\n                                        children: \"Blog\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"https://github.com/zainulabedeen123/best-saas-kit\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"text-sm text-white/70 hover:text-white flex items-center space-x-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                clipRule: \"evenodd\",\n                                                d: \"M12 2C6.477 2 2 6.477 2 12c0 4.42 2.87 8.17 6.84 ********.66-.23.66-.5v-1.69c-2.77.6-3.36-1.34-3.36-1.34-.46-1.16-1.11-1.47-1.11-1.47-.91-.62.07-.6.07-.6 1 .07 1.53 1.03 1.53 1.03.87 1.52 2.34 1.07 **********-.65.35-1.09.63-1.34-2.22-.25-4.55-1.11-4.55-4.92 0-1.11.38-2 1.03-2.71-.1-.25-.45-1.29.1-2.64 0 0 .84-.27 2.75 1.02.79-.22 1.65-.33 2.5-.33.85 0 1.71.11 2.5.33 1.91-1.29 2.75-1.02 2.75-1.02.55 1.35.2 2.39.1 ********** 1.03 1.6 1.03 2.71 0 3.82-2.34 4.66-4.57 **********.69.92.69 1.85V21c0 .***********.5C19.14 20.16 22 16.42 22 12A10 10 0 0012 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth\",\n                                        className: \"text-sm text-white/70 hover:text-white\",\n                                        children: \"Sign in\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth?view=sign-up\",\n                                        className: \"bg-[#FFBE1A] text-black text-sm px-4 py-2 rounded-lg hover:bg-[#FFBE1A]/90\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-32 pb-16 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center px-3 py-1 rounded-full bg-white/5 border border-white/10 text-sm text-white/70 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-2\",\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Ship faster with our SaaS Kit\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl sm:text-6xl font-bold text-white leading-[1.1] tracking-tight mb-6\",\n                                        children: [\n                                            \"Ship your startup\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 34\n                                            }, this),\n                                            \"in days, not weeks\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-white/70 mb-8 leading-relaxed\",\n                                        children: \"The fastest way to build and deploy your startup with production-ready code\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-white/5 px-3 py-1 rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex\",\n                                                    children: '★★★★★'.split('').map((star, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-[#FFBE1A]\",\n                                                            children: star\n                                                        }, i, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-[#FFBE1A] font-medium\",\n                                                    children: \"4.9/5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-2 text-white/30\",\n                                                    children: \"•\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white/70\",\n                                                    children: \"from 1000+ reviews\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/auth?view=sign-up\",\n                                                className: \"inline-flex justify-center items-center px-6 py-3 rounded-lg bg-[#FFBE1A] text-black font-medium hover:bg-[#FFBE1A]/90 transition-colors\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"https://github.com/zainulabedeen123/best-saas-kit\",\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"inline-flex justify-center items-center px-6 py-3 rounded-lg border border-white/10 text-white font-medium hover:bg-white/5 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            clipRule: \"evenodd\",\n                                                            d: \"M12 2C6.477 2 2 6.477 2 12c0 4.42 2.87 8.17 6.84 ********.66-.23.66-.5v-1.69c-2.77.6-3.36-1.34-3.36-1.34-.46-1.16-1.11-1.47-1.11-1.47-.91-.62.07-.6.07-.6 1 .07 1.53 1.03 1.53 1.03.87 1.52 2.34 1.07 **********-.65.35-1.09.63-1.34-2.22-.25-4.55-1.11-4.55-4.92 0-1.11.38-2 1.03-2.71-.1-.25-.45-1.29.1-2.64 0 0 .84-.27 2.75 1.02.79-.22 1.65-.33 2.5-.33.85 0 1.71.11 2.5.33 1.91-1.29 2.75-1.02 2.75-1.02.55 1.35.2 2.39.1 ********** 1.03 1.6 1.03 2.71 0 3.82-2.34 4.66-4.57 **********.69.92.69 1.85V21c0 .***********.5C19.14 20.16 22 16.42 22 12A10 10 0 0012 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Github Repo\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-[400px] lg:h-[500px]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: \"/Saas-Header.png\",\n                                    alt: \"SaaS Platform Preview\",\n                                    fill: true,\n                                    className: \"object-contain\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-[#4ADE80] text-sm font-mono\",\n                                children: 'const launch_time = \"01:19 AM\";'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-5xl font-bold mb-6 bg-gradient-to-r from-white/90 to-white/60 bg-clip-text text-transparent\",\n                                        children: [\n                                            \"Supercharge your app instantly,\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 48\n                                            }, this),\n                                            \"launch faster, make $\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-white/60 max-w-3xl\",\n                                        children: \"Login users, process payments and send emails at lightspeed. Spend your time building your startup, not integrating APIs. ShipFast provides you with the boilerplate code you need to launch, FAST.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full bg-white/5 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"@\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: \"Emails\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full bg-white/5 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83D\\uDCB3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: \"Payments\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full bg-white/5 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83D\\uDC64\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: \"Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full bg-white/5 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83D\\uDDC4️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: \"Database\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full bg-white/5 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83D\\uDCC4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: \"SEO\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full bg-white/5 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83C\\uDFA8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: \"Style\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full bg-white/5 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"⋯\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FFBE1A] text-sm\",\n                                                children: \"More\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 text-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-[#4ADE80]\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/80\",\n                                                children: \"Tips to write copy that sells\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-[#4ADE80]\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/80\",\n                                                children: \"Discord community to stay accountable\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-[#4ADE80]\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/80\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#FFBE1A]\",\n                                                        children: \"Crisp\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" customer support (auto show/hide, variables...)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-[#4ADE80]\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/80\",\n                                                children: \"Collect emails for a waitlist if your product isn't ready\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-[#4ADE80]\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/80\",\n                                                children: \"Prompts to generate terms & privacy policy with ChatGPT\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-[#4ADE80]\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/80\",\n                                                children: \"Copy paste code templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-[#4ADE80]\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/80\",\n                                                children: \"Dead simple tutorials\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-[#4ADE80] text-lg\",\n                                children: \"Time saved: ∞ hours\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#1A1311] rounded-3xl p-10 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-[#FF6B6B] font-medium\",\n                                        children: [\n                                            \"4 hrs \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"to set up emails\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 65\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"+ 6 hrs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"designing a landing page\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 80\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"+ 4 hrs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"to handle Stripe webhooks\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 80\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"+ 2 hrs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"for SEO tags\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 80\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"+ 1 hr\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"applying for Google Oauth\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 79\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"+ 3 hrs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"for DNS records\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 80\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"+ 2 hrs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"for protected API routes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 80\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"+ ∞ hrs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"overthinking...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 80\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-3 flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"= 22+ hours\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"of headaches\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83C\\uDF27️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center text-white/60 gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"There's an easier way\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 380,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-white/5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-white mb-8\",\n                            children: \"Watch Our Launch Announcement\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"aspect-video w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                className: \"w-full h-full rounded-2xl shadow-2xl\",\n                                src: \"https://www.youtube.com/embed/JiXy9JGkGzo\",\n                                title: \"Best SAAS Kit Pro Launch Announcement\",\n                                allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n                                allowFullScreen: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-white text-center mb-12\",\n                            children: [\n                                \"Save hours of repetitive code,\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 43\n                                }, this),\n                                \"ship fast, get profitable\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: pricingPlans.map((plan, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PricingCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    ...plan\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 429,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white mb-4\",\n                                    children: \"Get in Touch\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-white/60 max-w-2xl mx-auto\",\n                                    children: \"Have questions about our product? Need help getting started? We're here to help.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-white mb-4\",\n                                                    children: \"Contact Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white/60 mb-6\",\n                                                    children: \"Fill out the form and we'll get back to you within 24 hours.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-white/5 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-6 h-6 text-[#FFBE1A]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-white font-medium mb-1\",\n                                                                    children: \"Email\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"mailto:<EMAIL>\",\n                                                                    className: \"text-white/60 hover:text-[#FFBE1A]\",\n                                                                    children: \"<EMAIL>\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-white/5 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-6 h-6 text-[#FFBE1A]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-white font-medium mb-1\",\n                                                                    children: \"Phone\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"tel:+1234567890\",\n                                                                    className: \"text-white/60 hover:text-[#FFBE1A]\",\n                                                                    children: \"+1 (234) 567-890\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-white/5 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-6 h-6 text-[#FFBE1A]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-white font-medium mb-1\",\n                                                                    children: \"Office\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white/60\",\n                                                                    children: [\n                                                                        \"123 Innovation Street\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 44\n                                                                        }, this),\n                                                                        \"San Francisco, CA 94107\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-white font-medium mb-4\",\n                                                    children: \"Follow Us\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#FFBE1A]\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#FFBE1A]\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#FFBE1A]\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm5.894 8.221l-1.97 9.28c-.145.658-.537.818-1.084.508l-3-2.21-1.446 1.394c-.14.18-.357.223-.548.223l.188-2.85 5.18-4.68c.223-.204-.054-.31-.346-.106l-6.4 4.02-2.76-.92c-.6-.183-.612-.6.125-.89l10.782-4.156c.5-.183.94.114.78.89z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#FFBE1A]\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M12 0C5.374 0 0 5.373 0 12c0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/[0.02] rounded-2xl p-8 border border-white/5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"first_name\",\n                                                                className: \"block text-sm font-medium text-white mb-2\",\n                                                                children: \"First Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"first_name\",\n                                                                className: \"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]/50 focus:border-[#FFBE1A] text-white placeholder-white/40\",\n                                                                placeholder: \"John\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"last_name\",\n                                                                className: \"block text-sm font-medium text-white mb-2\",\n                                                                children: \"Last Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"last_name\",\n                                                                className: \"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]/50 focus:border-[#FFBE1A] text-white placeholder-white/40\",\n                                                                placeholder: \"Doe\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"email\",\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        id: \"email\",\n                                                        className: \"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]/50 focus:border-[#FFBE1A] text-white placeholder-white/40\",\n                                                        placeholder: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"subject\",\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Subject\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"subject\",\n                                                        className: \"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]/50 focus:border-[#FFBE1A] text-white placeholder-white/40\",\n                                                        placeholder: \"How can we help?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"message\",\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Message\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"message\",\n                                                        rows: 4,\n                                                        className: \"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]/50 focus:border-[#FFBE1A] text-white placeholder-white/40\",\n                                                        placeholder: \"Your message...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"w-full px-6 py-3 bg-[#FFBE1A] text-black font-medium rounded-lg hover:bg-[#FFBE1A]/90 transition-colors\",\n                                                children: \"Send Message\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 443,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-3xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-white text-center mb-12\",\n                            children: \"Frequently Asked Questions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: faqs.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FaqItem__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    ...faq\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 612,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 608,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 607,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-24 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl font-bold text-white mb-4\",\n                                children: [\n                                    \"5000+ makers built AI tools,\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 43\n                                    }, this),\n                                    \"SaaS, and more\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 623,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-[#111111] rounded-xl p-6 border border-white/5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center text-white font-medium\",\n                                                    children: \"SC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: \"Sarah Chen\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"CTO at TechStart\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 mb-6\",\n                                            children: \"This toolkit saved us months of development time. We launched our MVP in just 2 weeks!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"3 months\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 647,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"Time Saved\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-[#4ADE80]\",\n                                                            children: \"300%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"ROI\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-[#111111] rounded-xl p-6 border border-white/5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center text-white font-medium\",\n                                                    children: \"MJ\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: \"Mike Johnson\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"Lead Developer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 mb-6\",\n                                            children: \"The code quality is exceptional. It's like having a senior developer on the team.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"50+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"Components\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-[#4ADE80]\",\n                                                            children: \"100%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"TypeScript\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 678,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-[#111111] rounded-xl p-6 border border-white/5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 rounded-full bg-gradient-to-br from-amber-500 to-orange-500 flex items-center justify-center text-white font-medium\",\n                                                    children: \"LP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: \"Lisa Park\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"Founder at AppLabs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 mb-6\",\n                                            children: \"Best investment we made for our startup. The support is amazing too!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"24/7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"Support\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-[#4ADE80]\",\n                                                            children: \"95%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"Satisfaction\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 702,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-16 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center bg-white/5 px-4 py-2 rounded-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex -space-x-2 mr-3\",\n                                        children: [\n                                            ...Array(5)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-full border-2 border-[#111111] bg-gradient-to-br from-purple-500 to-pink-500\",\n                                                style: {\n                                                    transform: `translateX(${i * -4}px)`\n                                                }\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/60 text-sm\",\n                                        children: [\n                                            \"Join \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-medium\",\n                                                children: \"5,000+\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" makers\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 711,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 622,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 621,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"py-20 px-4 border-t border-white/5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-4\",\n                                        children: \"Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/features\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Features\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/pricing\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Pricing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/docs\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Documentation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 739,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/changelog\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Changelog\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 740,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 736,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-4\",\n                                        children: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/about\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"About\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/blog\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Blog\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 747,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/careers\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Careers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/contact\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Contact\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 749,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 745,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-4\",\n                                        children: \"Resources\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"https://github.com/zainulabedeen123/best-saas-kit/discussions\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Community\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/help\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Help Center\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/status\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/terms\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Terms of Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 765,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 752,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-4\",\n                                        children: \"Connect\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 769,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"https://twitter.com\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Twitter\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"https://github.com/zainulabedeen123/best-saas-kit\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"GitHub\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"https://discord.com\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Discord\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 773,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/newsletter\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Newsletter\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 768,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 733,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 732,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 731,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9be5cd341dcb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUHJpeWFkYXJzaGFuIFRpd2FyaVxcRG93bmxvYWRzXFxWRyBLb3NoXzEuMFxcVkcgS29zaF8xLjBcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5YmU1Y2QzNDFkY2JcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst metadata = {\n    title: 'Best SAAS Kit Pro',\n    description: 'The ultimate starter kit for your SAAS project'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} min-h-screen bg-primary text-secondary antialiased`,\n            suppressHydrationWarning: true,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzQjtBQUdoQkE7QUFFQyxNQUFNQyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0Msd0JBQXdCO2tCQUN0Qyw0RUFBQ0M7WUFBS0MsV0FBVyxHQUFHViwySkFBZSxDQUFDLG1EQUFtRCxDQUFDO1lBQUVRLHdCQUF3QjtzQkFDL0dIOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFByaXlhZGFyc2hhbiBUaXdhcmlcXERvd25sb2Fkc1xcVkcgS29zaF8xLjBcXFZHIEtvc2hfMS4wXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xyXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXHJcblxyXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXHJcblxyXG5leHBvcnQgY29uc3QgbWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6ICdCZXN0IFNBQVMgS2l0IFBybycsXHJcbiAgZGVzY3JpcHRpb246ICdUaGUgdWx0aW1hdGUgc3RhcnRlciBraXQgZm9yIHlvdXIgU0FBUyBwcm9qZWN0JyxcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXHJcbn0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGh0bWwgbGFuZz1cImVuXCIgc3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nPlxyXG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2Ake2ludGVyLmNsYXNzTmFtZX0gbWluLWgtc2NyZWVuIGJnLXByaW1hcnkgdGV4dC1zZWNvbmRhcnkgYW50aWFsaWFzZWRgfSBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc+XHJcbiAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\VG Kosh_1.0\\VG Kosh_1.0\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxQcml5YWRhcnNoYW4gVGl3YXJpXFxEb3dubG9hZHNcXFZHIEtvc2hfMS4wXFxWRyBLb3NoXzEuMFxcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/@heroicons","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/@stripe"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();