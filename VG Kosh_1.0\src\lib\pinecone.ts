import { Pinecone } from '@pinecone-database/pinecone';
import OpenAI from 'openai';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';

// Initialize Pinecone client
const pinecone = new Pinecone({
  apiKey: process.env.PINECONE_API_KEY!,
});

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY!,
});

// Get the Pinecone index
export const getIndex = () => {
  const indexName = process.env.PINECONE_INDEX_NAME || 'vg-kosh-documents';
  return pinecone.index(indexName);
};

// Generate embeddings using OpenAI
export async function generateEmbedding(text: string): Promise<number[]> {
  try {
    const response = await openai.embeddings.create({
      model: 'text-embedding-ada-002',
      input: text,
    });
    return response.data[0].embedding;
  } catch (error) {
    console.error('Error generating embedding:', error);
    throw new Error('Failed to generate embedding');
  }
}

// Split text into chunks
export function splitText(text: string, chunkSize: number = 500, chunkOverlap: number = 50) {
  const splitter = new RecursiveCharacterTextSplitter({
    chunkSize,
    chunkOverlap,
  });
  return splitter.splitText(text);
}

// Upload document chunks to Pinecone
export async function uploadDocument(
  documentId: string,
  text: string,
  metadata: Record<string, any> = {}
) {
  try {
    const index = getIndex();
    const chunks = splitText(text);
    
    const vectors = [];
    
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      const embedding = await generateEmbedding(chunk);
      
      vectors.push({
        id: `${documentId}-chunk-${i}`,
        values: embedding,
        metadata: {
          ...metadata,
          text: chunk,
          chunkIndex: i,
          documentId,
          uploadTimestamp: Date.now(),
        },
      });
    }
    
    // Upload in batches to avoid rate limits
    const batchSize = 50;
    for (let i = 0; i < vectors.length; i += batchSize) {
      const batch = vectors.slice(i, i + batchSize);
      await index.upsert(batch);
      
      // Small delay to avoid rate limits
      if (i + batchSize < vectors.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    return {
      success: true,
      chunksUploaded: vectors.length,
      documentId,
    };
  } catch (error) {
    console.error('Error uploading document:', error);
    throw new Error('Failed to upload document to Pinecone');
  }
}

// Search for similar documents
export async function searchDocuments(
  query: string,
  topK: number = 5,
  filter?: Record<string, any>
) {
  try {
    const index = getIndex();
    const queryEmbedding = await generateEmbedding(query);
    
    const searchResponse = await index.query({
      vector: queryEmbedding,
      topK,
      includeMetadata: true,
      filter,
    });
    
    return searchResponse.matches?.map(match => ({
      id: match.id,
      score: match.score,
      text: match.metadata?.text,
      documentId: match.metadata?.documentId,
      chunkIndex: match.metadata?.chunkIndex,
      metadata: match.metadata,
    })) || [];
  } catch (error) {
    console.error('Error searching documents:', error);
    throw new Error('Failed to search documents');
  }
}

// Delete document from Pinecone
export async function deleteDocument(documentId: string) {
  try {
    const index = getIndex();
    
    // First, find all chunks for this document
    const searchResponse = await index.query({
      vector: new Array(1536).fill(0), // Dummy vector for metadata-only search
      topK: 10000, // Large number to get all chunks
      includeMetadata: true,
      filter: { documentId },
    });
    
    if (searchResponse.matches && searchResponse.matches.length > 0) {
      const chunkIds = searchResponse.matches.map(match => match.id);
      await index.deleteMany(chunkIds);
    }
    
    return { success: true, deletedChunks: searchResponse.matches?.length || 0 };
  } catch (error) {
    console.error('Error deleting document:', error);
    throw new Error('Failed to delete document from Pinecone');
  }
}

// Get document statistics
export async function getIndexStats() {
  try {
    const index = getIndex();
    const stats = await index.describeIndexStats();
    return stats;
  } catch (error) {
    console.error('Error getting index stats:', error);
    throw new Error('Failed to get index statistics');
  }
}

// Types for better TypeScript support
export interface DocumentChunk {
  id: string;
  score?: number;
  text: string;
  documentId: string;
  chunkIndex: number;
  metadata: Record<string, any>;
}

export interface UploadResult {
  success: boolean;
  chunksUploaded: number;
  documentId: string;
}

export interface SearchResult {
  id: string;
  score?: number;
  text: string;
  documentId: string;
  chunkIndex: number;
  metadata: Record<string, any>;
}
