"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/docs/page",{

/***/ "(app-pages-browser)/./app/docs/page.tsx":
/*!***************************!*\
  !*** ./app/docs/page.tsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Documentation sections\nconst sections = [\n    {\n        id: 'video-tutorial',\n        title: 'Video Tutorial',\n        content: [\n            {\n                title: 'Getting Started Tutorial',\n                description: 'Watch our comprehensive video tutorial to get started with Best SAAS Kit Pro.',\n                videoId: 'zXwkOqIDzSU'\n            }\n        ]\n    },\n    {\n        id: 'getting-started',\n        title: 'Getting Started',\n        content: [\n            {\n                title: 'Installation',\n                steps: [\n                    {\n                        title: 'Clone the Repository',\n                        description: 'Start by cloning the repository to your local machine.',\n                        code: 'git clone https://github.com/VG-2020dl/VG-Kosh.git'\n                    },\n                    {\n                        title: 'Install Dependencies',\n                        description: 'Navigate to the project directory and install the required dependencies.',\n                        code: 'cd best-saas-kit\\nnpm install'\n                    },\n                    {\n                        title: 'Environment Setup',\n                        description: 'Create a .env.local file in the root directory with your environment variables.',\n                        code: \"# App\\nNEXT_PUBLIC_APP_URL=http://localhost:3000\\n\\n# Supabase\\nNEXT_PUBLIC_SUPABASE_URL=your_supabase_url\\nNEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key\\nSUPABASE_SERVICE_ROLE_KEY=your_service_role_key\\n\\n# Stripe\\nSTRIPE_SECRET_KEY=your_stripe_secret_key\\nSTRIPE_WEBHOOK_SECRET=your_webhook_secret\\nNEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_publishable_key\\n\\n# Email (Resend)\\nRESEND_API_KEY=your_resend_api_key\"\n                    },\n                    {\n                        title: 'Start Development Server',\n                        description: 'Run the development server to start working on your project.',\n                        code: 'npm run dev'\n                    }\n                ]\n            },\n            {\n                title: 'Project Structure',\n                description: \"The project follows a clean and organized structure:\\n\\n- /app - Next.js app router pages and API routes\\n- /components - Reusable React components\\n- /lib - Utility functions and configurations\\n- /public - Static assets\\n- /styles - Global styles and Tailwind configuration\"\n            }\n        ]\n    },\n    {\n        id: 'authentication',\n        title: 'Authentication',\n        content: [\n            {\n                title: 'Setting up Supabase Auth',\n                steps: [\n                    {\n                        title: 'Create Supabase Project',\n                        description: 'Go to Supabase dashboard and create a new project.'\n                    },\n                    {\n                        title: 'Configure Auth Settings',\n                        description: 'Enable Email/Password authentication in your Supabase project settings.'\n                    },\n                    {\n                        title: 'Add Environment Variables',\n                        description: 'Copy your Supabase URL and anon key to your .env.local file.'\n                    }\n                ]\n            },\n            {\n                title: 'Authentication Flow',\n                description: \"The authentication flow includes:\\n- Sign up with email/password\\n- Email verification\\n- Sign in with email/password\\n- Password reset functionality\\n- Protected routes and middleware\"\n            }\n        ]\n    },\n    {\n        id: 'email',\n        title: 'Email Integration',\n        content: [\n            {\n                title: 'Resend Email Setup',\n                steps: [\n                    {\n                        title: 'Create Resend Account',\n                        description: 'Sign up for a Resend account at https://resend.com and get your API key.'\n                    },\n                    {\n                        title: 'Configure API Key',\n                        description: 'Add your Resend API key to the .env.local file.',\n                        code: 'RESEND_API_KEY=your_resend_api_key'\n                    },\n                    {\n                        title: 'Verify Domain',\n                        description: 'Add and verify your sending domain in the Resend dashboard for better deliverability.'\n                    }\n                ]\n            },\n            {\n                title: 'Email Features',\n                description: \"Integrated email functionality includes:\\n- Welcome emails for new sign-ups\\n- Beautiful, responsive email templates\\n- React-based email components\\n- Email delivery tracking\\n- Custom email templates for various notifications\"\n            },\n            {\n                title: 'Customizing Email Templates',\n                description: 'Email templates are located in src/lib/email/templates/ and can be customized using React Email components.',\n                steps: [\n                    {\n                        title: 'Template Structure',\n                        description: 'Each email template is a React component using @react-email/components.',\n                        code: \"import { \\n  Body, Container, Head, Html, \\n  Preview, Section, Text \\n} from '@react-email/components';\\n\\nexport const EmailTemplate = ({ \\n  username = 'there' \\n}) => (\\n  <Html>\\n    <Head />\\n    <Preview>Welcome message</Preview>\\n    <Body>\\n      <Container>\\n        <Text>Hello {username}!</Text>\\n      </Container>\\n    </Body>\\n  </Html>\\n);\"\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: 'payments',\n        title: 'Payments',\n        content: [\n            {\n                title: 'Stripe Integration',\n                steps: [\n                    {\n                        title: 'Create Stripe Account',\n                        description: 'Sign up for a Stripe account and get your API keys.'\n                    },\n                    {\n                        title: 'Configure Webhook',\n                        description: 'Set up Stripe webhook to handle payment events.',\n                        code: 'stripe listen --forward-to localhost:3000/api/webhooks/stripe'\n                    },\n                    {\n                        title: 'Add Products',\n                        description: 'Create your products and price plans in the Stripe dashboard.'\n                    }\n                ]\n            },\n            {\n                title: 'Subscription Management',\n                description: \"Features included:\\n- Subscription creation and management\\n- Usage-based billing\\n- Credit system integration\\n- Payment history\\n- Invoice management\"\n            }\n        ]\n    }\n];\nfunction DocsPage() {\n    var _sections_find;\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('video-tutorial');\n    const isVideoContent = (item)=>{\n        return 'videoId' in item;\n    };\n    const isStepsContent = (item)=>{\n        return 'steps' in item;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#000000]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-[#000000]/80 backdrop-blur-sm border-b border-white/5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"text-white font-bold text-xl\",\n                                children: \"Documentation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"text-white/80 hover:text-white transition-colors\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/features\",\n                                        className: \"text-white/80 hover:text-white transition-colors\",\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth\",\n                                        className: \"text-[#FFBE1A] hover:text-[#FFBE1A]/80 transition-colors\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 pt-24 pb-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-64 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sticky top-24 space-y-2\",\n                                children: sections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveSection(section.id),\n                                        className: \"w-full text-left px-4 py-2 rounded-lg transition-colors \".concat(activeSection === section.id ? 'bg-white/10 text-white' : 'text-white/60 hover:bg-white/5 hover:text-white'),\n                                        children: section.title\n                                    }, section.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 max-w-3xl\",\n                            children: (_sections_find = sections.find((section)=>section.id === activeSection)) === null || _sections_find === void 0 ? void 0 : _sections_find.content.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-white mb-6\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 19\n                                        }, this),\n                                        isVideoContent(item) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-video mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                                className: \"w-full h-full rounded-lg\",\n                                                src: \"https://www.youtube.com/embed/\".concat(item.videoId),\n                                                title: \"Tutorial Video\",\n                                                allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n                                                allowFullScreen: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 21\n                                        }, this),\n                                        item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose prose-invert mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80 whitespace-pre-line\",\n                                                children: item.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 21\n                                        }, this),\n                                        isStepsContent(item) && item.steps && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: item.steps.map((step, stepIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 rounded-lg p-6 border border-white/10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 rounded-full bg-[#FFBE1A] text-black flex items-center justify-center flex-shrink-0 mt-1\",\n                                                                children: stepIndex + 1\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-semibold text-white\",\n                                                                        children: step.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white/80\",\n                                                                        children: step.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    step.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                        className: \"bg-black/50 p-4 rounded-lg overflow-x-auto\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                            className: \"text-white/90 whitespace-pre\",\n                                                                            children: step.code\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 332,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, stepIndex, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(DocsPage, \"hml/cwUyMR0xIIB5IeQt37NFjoQ=\");\n_c = DocsPage;\nvar _c;\n$RefreshReg$(_c, \"DocsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/docs/page.tsx\n"));

/***/ })

});