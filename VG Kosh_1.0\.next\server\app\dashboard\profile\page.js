/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/profile/page";
exports.ids = ["app/dashboard/profile/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fprofile%2Fpage&page=%2Fdashboard%2Fprofile%2Fpage&appPaths=%2Fdashboard%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprofile%2Fpage.tsx&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fprofile%2Fpage&page=%2Fdashboard%2Fprofile%2Fpage&appPaths=%2Fdashboard%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprofile%2Fpage.tsx&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/layout.tsx */ \"(rsc)/./app/dashboard/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/profile/page.tsx */ \"(rsc)/./app/dashboard/profile/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'profile',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/profile/page\",\n        pathname: \"/dashboard/profile\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fprofile%2Fpage&page=%2Fdashboard%2Fprofile%2Fpage&appPaths=%2Fdashboard%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprofile%2Fpage.tsx&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/layout.tsx */ \"(rsc)/./app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1ByaXlhZGFyc2hhbiUyMFRpd2FyaSU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1ZHJTIwS29zaF8xLjAlNUMlNUNWRyUyMEtvc2hfMS4wJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQW9JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxQcml5YWRhcnNoYW4gVGl3YXJpXFxcXERvd25sb2Fkc1xcXFxWRyBLb3NoXzEuMFxcXFxWRyBLb3NoXzEuMFxcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/layout.tsx */ \"(ssr)/./app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1ByaXlhZGFyc2hhbiUyMFRpd2FyaSU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1ZHJTIwS29zaF8xLjAlNUMlNUNWRyUyMEtvc2hfMS4wJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQW9JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxQcml5YWRhcnNoYW4gVGl3YXJpXFxcXERvd25sb2Fkc1xcXFxWRyBLb3NoXzEuMFxcXFxWRyBLb3NoXzEuMFxcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/profile/page.tsx */ \"(rsc)/./app/dashboard/profile/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1ByaXlhZGFyc2hhbiUyMFRpd2FyaSU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1ZHJTIwS29zaF8xLjAlNUMlNUNWRyUyMEtvc2hfMS4wJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDcHJvZmlsZSU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0S0FBMkkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFByaXlhZGFyc2hhbiBUaXdhcmlcXFxcRG93bmxvYWRzXFxcXFZHIEtvc2hfMS4wXFxcXFZHIEtvc2hfMS4wXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccHJvZmlsZVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/profile/page.tsx */ \"(ssr)/./app/dashboard/profile/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1ByaXlhZGFyc2hhbiUyMFRpd2FyaSU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1ZHJTIwS29zaF8xLjAlNUMlNUNWRyUyMEtvc2hfMS4wJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDcHJvZmlsZSU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0S0FBMkkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFByaXlhZGFyc2hhbiBUaXdhcmlcXFxcRG93bmxvYWRzXFxcXFZHIEtvc2hfMS4wXFxcXFZHIEtvc2hfMS4wXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccHJvZmlsZVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/layout.tsx":
/*!**********************************!*\
  !*** ./app/dashboard/layout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _components_dashboard_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/Header */ \"(ssr)/./components/dashboard/Header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst navigation = [\n    {\n        name: 'Overview',\n        href: '/dashboard',\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'Analytics',\n        href: '/dashboard/analytics',\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'Profile',\n        href: '/dashboard/profile',\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'Documents',\n        href: '/dashboard/documents',\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'Settings',\n        href: '/dashboard/settings',\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    }\n];\nfunction DashboardLayout({ children }) {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__.createClientComponentClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardLayout.useEffect\": ()=>{\n            const checkSession = {\n                \"DashboardLayout.useEffect.checkSession\": async ()=>{\n                    try {\n                        const { data: { session } } = await supabase.auth.getSession();\n                        if (!session) {\n                            router.replace('/auth');\n                            return;\n                        }\n                        setIsAuthenticated(true);\n                    } catch (error) {\n                        console.error('Error checking session:', error);\n                        router.replace('/auth');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"DashboardLayout.useEffect.checkSession\"];\n            checkSession();\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"DashboardLayout.useEffect\": (_event, session)=>{\n                    if (!session) {\n                        router.replace('/auth');\n                        setIsAuthenticated(false);\n                    } else {\n                        setIsAuthenticated(true);\n                    }\n                    setIsLoading(false);\n                }\n            }[\"DashboardLayout.useEffect\"]);\n            return ({\n                \"DashboardLayout.useEffect\": ()=>{\n                    subscription.unsubscribe();\n                }\n            })[\"DashboardLayout.useEffect\"];\n        }\n    }[\"DashboardLayout.useEffect\"], [\n        router,\n        supabase.auth\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-[calc(100vh-4rem)] pt-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed left-0 w-64 h-[calc(100vh-4rem)] bg-[#111111] border-r border-white/5 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"p-4 space-y-1\",\n                            children: navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: item.href,\n                                    className: `\n                    flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors\n                    ${isActive ? 'bg-white/10 text-white' : 'text-white/60 hover:bg-white/5 hover:text-white'}\n                  `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"w-5 h-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-64 flex-1 overflow-y-auto p-4 sm:p-6 lg:p-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvZGFzaGJvYXJkL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDYTtBQUNtQjtBQUMvQztBQU9RO0FBQ2M7QUFRbEQsTUFBTVksYUFBd0I7SUFDNUI7UUFBRUMsTUFBTTtRQUFZQyxNQUFNO1FBQWNDLE1BQU1ULDZKQUFRQTtJQUFDO0lBQ3ZEO1FBQUVPLE1BQU07UUFBYUMsTUFBTTtRQUF3QkMsTUFBTVIsNkpBQVlBO0lBQUM7SUFDdEU7UUFBRU0sTUFBTTtRQUFXQyxNQUFNO1FBQXNCQyxNQUFNUCw2SkFBY0E7SUFBQztJQUNwRTtRQUFFSyxNQUFNO1FBQWFDLE1BQU07UUFBd0JDLE1BQU1OLDZKQUFnQkE7SUFBQztJQUMxRTtRQUFFSSxNQUFNO1FBQVlDLE1BQU07UUFBdUJDLE1BQU1MLDhKQUFhQTtJQUFDO0NBQ3RFO0FBRWMsU0FBU00sZ0JBQWdCLEVBQ3RDQyxRQUFRLEVBR1Q7SUFDQyxNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR25CLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ29CLGlCQUFpQkMsbUJBQW1CLEdBQUdyQiwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNc0IsU0FBU3BCLDBEQUFTQTtJQUN4QixNQUFNcUIsV0FBV3BCLDREQUFXQTtJQUM1QixNQUFNcUIsV0FBV3BCLDBGQUEyQkE7SUFFNUNILGdEQUFTQTtxQ0FBQztZQUNSLE1BQU13QjswREFBZTtvQkFDbkIsSUFBSTt3QkFDRixNQUFNLEVBQUVDLE1BQU0sRUFBRUMsT0FBTyxFQUFFLEVBQUUsR0FBRyxNQUFNSCxTQUFTSSxJQUFJLENBQUNDLFVBQVU7d0JBQzVELElBQUksQ0FBQ0YsU0FBUzs0QkFDWkwsT0FBT1EsT0FBTyxDQUFDOzRCQUNmO3dCQUNGO3dCQUNBVCxtQkFBbUI7b0JBQ3JCLEVBQUUsT0FBT1UsT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7d0JBQ3pDVCxPQUFPUSxPQUFPLENBQUM7b0JBQ2pCLFNBQVU7d0JBQ1JYLGFBQWE7b0JBQ2Y7Z0JBQ0Y7O1lBRUFNO1lBRUEsTUFBTSxFQUFFQyxNQUFNLEVBQUVPLFlBQVksRUFBRSxFQUFFLEdBQUdULFNBQVNJLElBQUksQ0FBQ00saUJBQWlCOzZDQUFDLENBQUNDLFFBQVFSO29CQUMxRSxJQUFJLENBQUNBLFNBQVM7d0JBQ1pMLE9BQU9RLE9BQU8sQ0FBQzt3QkFDZlQsbUJBQW1CO29CQUNyQixPQUFPO3dCQUNMQSxtQkFBbUI7b0JBQ3JCO29CQUNBRixhQUFhO2dCQUNmOztZQUVBOzZDQUFPO29CQUNMYyxhQUFhRyxXQUFXO2dCQUMxQjs7UUFDRjtvQ0FBRztRQUFDZDtRQUFRRSxTQUFTSSxJQUFJO0tBQUM7SUFFMUIsSUFBSVYsV0FBVztRQUNiLHFCQUNFLDhEQUFDbUI7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQWE7Ozs7Ozs7Ozs7O0lBR2xDO0lBRUEsSUFBSSxDQUFDbEIsaUJBQWlCO1FBQ3BCLE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDaUI7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUMzQixvRUFBTUE7Ozs7OzBCQUVQLDhEQUFDMEI7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0M7NEJBQUlELFdBQVU7c0NBQ1oxQixXQUFXNEIsR0FBRyxDQUFDLENBQUNDO2dDQUNmLE1BQU1DLFdBQVduQixhQUFha0IsS0FBSzNCLElBQUk7Z0NBQ3ZDLHFCQUNFLDhEQUFDVCxrREFBSUE7b0NBRUhTLE1BQU0yQixLQUFLM0IsSUFBSTtvQ0FDZndCLFdBQVcsQ0FBQzs7b0JBRVYsRUFBRUksV0FDRSwyQkFDQSxrREFDSDtrQkFDSCxDQUFDOztzREFFRCw4REFBQ0QsS0FBSzFCLElBQUk7NENBQUN1QixXQUFVOzs7Ozs7d0NBQ3BCRyxLQUFLNUIsSUFBSTs7bUNBWEw0QixLQUFLNUIsSUFBSTs7Ozs7NEJBY3BCOzs7Ozs7Ozs7OztrQ0FLSiw4REFBQ3dCO3dCQUFJQyxXQUFVO2tDQUNackI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFByaXlhZGFyc2hhbiBUaXdhcmlcXERvd25sb2Fkc1xcVkcgS29zaF8xLjBcXFZHIEtvc2hfMS4wXFxhcHBcXGRhc2hib2FyZFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcclxuaW1wb3J0IHsgdXNlUm91dGVyLCB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcclxuaW1wb3J0IHsgY3JlYXRlQ2xpZW50Q29tcG9uZW50Q2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL2F1dGgtaGVscGVycy1uZXh0anMnXHJcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcclxuaW1wb3J0IHtcclxuICBIb21lSWNvbixcclxuICBDaGFydEJhckljb24sXHJcbiAgVXNlckNpcmNsZUljb24sXHJcbiAgRG9jdW1lbnRUZXh0SWNvbixcclxuICBDb2c2VG9vdGhJY29uLFxyXG59IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSdcclxuaW1wb3J0IEhlYWRlciBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL0hlYWRlcidcclxuXHJcbmludGVyZmFjZSBOYXZJdGVtIHtcclxuICBuYW1lOiBzdHJpbmdcclxuICBocmVmOiBzdHJpbmdcclxuICBpY29uOiB0eXBlb2YgSG9tZUljb25cclxufVxyXG5cclxuY29uc3QgbmF2aWdhdGlvbjogTmF2SXRlbVtdID0gW1xyXG4gIHsgbmFtZTogJ092ZXJ2aWV3JywgaHJlZjogJy9kYXNoYm9hcmQnLCBpY29uOiBIb21lSWNvbiB9LFxyXG4gIHsgbmFtZTogJ0FuYWx5dGljcycsIGhyZWY6ICcvZGFzaGJvYXJkL2FuYWx5dGljcycsIGljb246IENoYXJ0QmFySWNvbiB9LFxyXG4gIHsgbmFtZTogJ1Byb2ZpbGUnLCBocmVmOiAnL2Rhc2hib2FyZC9wcm9maWxlJywgaWNvbjogVXNlckNpcmNsZUljb24gfSxcclxuICB7IG5hbWU6ICdEb2N1bWVudHMnLCBocmVmOiAnL2Rhc2hib2FyZC9kb2N1bWVudHMnLCBpY29uOiBEb2N1bWVudFRleHRJY29uIH0sXHJcbiAgeyBuYW1lOiAnU2V0dGluZ3MnLCBocmVmOiAnL2Rhc2hib2FyZC9zZXR0aW5ncycsIGljb246IENvZzZUb290aEljb24gfSxcclxuXVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkTGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufToge1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcclxufSkge1xyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxyXG4gIGNvbnN0IFtpc0F1dGhlbnRpY2F0ZWQsIHNldElzQXV0aGVudGljYXRlZF0gPSB1c2VTdGF0ZShmYWxzZSlcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxyXG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKVxyXG4gIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50Q29tcG9uZW50Q2xpZW50KClcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGNoZWNrU2Vzc2lvbiA9IGFzeW5jICgpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCB7IGRhdGE6IHsgc2Vzc2lvbiB9IH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFNlc3Npb24oKVxyXG4gICAgICAgIGlmICghc2Vzc2lvbikge1xyXG4gICAgICAgICAgcm91dGVyLnJlcGxhY2UoJy9hdXRoJylcclxuICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH1cclxuICAgICAgICBzZXRJc0F1dGhlbnRpY2F0ZWQodHJ1ZSlcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjaGVja2luZyBzZXNzaW9uOicsIGVycm9yKVxyXG4gICAgICAgIHJvdXRlci5yZXBsYWNlKCcvYXV0aCcpXHJcbiAgICAgIH0gZmluYWxseSB7XHJcbiAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgY2hlY2tTZXNzaW9uKClcclxuXHJcbiAgICBjb25zdCB7IGRhdGE6IHsgc3Vic2NyaXB0aW9uIH0gfSA9IHN1cGFiYXNlLmF1dGgub25BdXRoU3RhdGVDaGFuZ2UoKF9ldmVudCwgc2Vzc2lvbikgPT4ge1xyXG4gICAgICBpZiAoIXNlc3Npb24pIHtcclxuICAgICAgICByb3V0ZXIucmVwbGFjZSgnL2F1dGgnKVxyXG4gICAgICAgIHNldElzQXV0aGVudGljYXRlZChmYWxzZSlcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXRJc0F1dGhlbnRpY2F0ZWQodHJ1ZSlcclxuICAgICAgfVxyXG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXHJcbiAgICB9KVxyXG5cclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIHN1YnNjcmlwdGlvbi51bnN1YnNjcmliZSgpXHJcbiAgICB9XHJcbiAgfSwgW3JvdXRlciwgc3VwYWJhc2UuYXV0aF0pXHJcblxyXG4gIGlmIChpc0xvYWRpbmcpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWJsYWNrIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCI+TG9hZGluZy4uLjwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIClcclxuICB9XHJcblxyXG4gIGlmICghaXNBdXRoZW50aWNhdGVkKSB7XHJcbiAgICByZXR1cm4gbnVsbFxyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWJsYWNrXCI+XHJcbiAgICAgIDxIZWFkZXIgLz5cclxuICAgICAgXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLVtjYWxjKDEwMHZoLTRyZW0pXSBwdC0xNlwiPlxyXG4gICAgICAgIHsvKiBTaWRlYmFyICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgbGVmdC0wIHctNjQgaC1bY2FsYygxMDB2aC00cmVtKV0gYmctWyMxMTExMTFdIGJvcmRlci1yIGJvcmRlci13aGl0ZS81IG92ZXJmbG93LXktYXV0b1wiPlxyXG4gICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJwLTQgc3BhY2UteS0xXCI+XHJcbiAgICAgICAgICAgIHtuYXZpZ2F0aW9uLm1hcCgoaXRlbSkgPT4ge1xyXG4gICAgICAgICAgICAgIGNvbnN0IGlzQWN0aXZlID0gcGF0aG5hbWUgPT09IGl0ZW0uaHJlZlxyXG4gICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICBrZXk9e2l0ZW0ubmFtZX1cclxuICAgICAgICAgICAgICAgICAgaHJlZj17aXRlbS5ocmVmfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BcclxuICAgICAgICAgICAgICAgICAgICBmbGV4IGl0ZW1zLWNlbnRlciBweC0zIHB5LTIgcm91bmRlZC1sZyB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzXHJcbiAgICAgICAgICAgICAgICAgICAgJHtpc0FjdGl2ZSBcclxuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXdoaXRlLzEwIHRleHQtd2hpdGUnIFxyXG4gICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC13aGl0ZS82MCBob3ZlcjpiZy13aGl0ZS81IGhvdmVyOnRleHQtd2hpdGUnXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICBgfVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8aXRlbS5pY29uIGNsYXNzTmFtZT1cInctNSBoLTUgbXItM1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgIHtpdGVtLm5hbWV9XHJcbiAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9KX1cclxuICAgICAgICAgIDwvbmF2PlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogTWFpbiBDb250ZW50ICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtNjQgZmxleC0xIG92ZXJmbG93LXktYXV0byBwLTQgc206cC02IGxnOnAtOFwiPlxyXG4gICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gIClcclxufSAiXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJ1c2VQYXRobmFtZSIsImNyZWF0ZUNsaWVudENvbXBvbmVudENsaWVudCIsIkxpbmsiLCJIb21lSWNvbiIsIkNoYXJ0QmFySWNvbiIsIlVzZXJDaXJjbGVJY29uIiwiRG9jdW1lbnRUZXh0SWNvbiIsIkNvZzZUb290aEljb24iLCJIZWFkZXIiLCJuYXZpZ2F0aW9uIiwibmFtZSIsImhyZWYiLCJpY29uIiwiRGFzaGJvYXJkTGF5b3V0IiwiY2hpbGRyZW4iLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJpc0F1dGhlbnRpY2F0ZWQiLCJzZXRJc0F1dGhlbnRpY2F0ZWQiLCJyb3V0ZXIiLCJwYXRobmFtZSIsInN1cGFiYXNlIiwiY2hlY2tTZXNzaW9uIiwiZGF0YSIsInNlc3Npb24iLCJhdXRoIiwiZ2V0U2Vzc2lvbiIsInJlcGxhY2UiLCJlcnJvciIsImNvbnNvbGUiLCJzdWJzY3JpcHRpb24iLCJvbkF1dGhTdGF0ZUNoYW5nZSIsIl9ldmVudCIsInVuc3Vic2NyaWJlIiwiZGl2IiwiY2xhc3NOYW1lIiwibmF2IiwibWFwIiwiaXRlbSIsImlzQWN0aXZlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/profile/page.tsx":
/*!****************************************!*\
  !*** ./app/dashboard/profile/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProfileSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ProfileSettings() {\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    const fetchProfile = async ()=>{\n        try {\n            setError(null);\n            // Get user data\n            const userResponse = await supabase.auth.getUser();\n            console.log('Auth response:', userResponse);\n            if (userResponse.error) {\n                console.error('Auth error:', userResponse.error);\n                throw new Error(`Authentication error: ${userResponse.error.message}`);\n            }\n            const user = userResponse.data.user;\n            if (!user) {\n                console.log('No user found, redirecting to auth');\n                router.push('/auth');\n                return;\n            }\n            console.log('User found:', user.id);\n            // First check if profile exists\n            const { data: profiles, error: profileError } = await supabase.from('profiles').select('id, username, full_name, avatar_url, website, email').eq('id', user.id);\n            console.log('Profile query response:', {\n                data: profiles,\n                error: profileError\n            });\n            if (profileError) {\n                console.error('Profile error:', profileError);\n                throw new Error(`Profile error: ${profileError.message}`);\n            }\n            // Handle multiple profiles case\n            if (profiles && profiles.length > 1) {\n                console.log('Multiple profiles found, cleaning up...');\n                // Delete duplicate profiles\n                const [keepProfile, ...duplicates] = profiles;\n                if (duplicates.length > 0) {\n                    const { error: deleteError } = await supabase.from('profiles').delete().in('id', duplicates.map((p)=>p.id));\n                    if (deleteError) {\n                        console.error('Error cleaning up duplicate profiles:', deleteError);\n                    }\n                }\n                setProfile(keepProfile);\n            } else if (!profiles || profiles.length === 0) {\n                console.log('No profile found, creating new profile...');\n                const { data: newProfile, error: insertError } = await supabase.from('profiles').insert([\n                    {\n                        id: user.id,\n                        username: '',\n                        full_name: '',\n                        avatar_url: null,\n                        website: '',\n                        email: user.email || ''\n                    }\n                ]).select().single();\n                console.log('New profile creation:', {\n                    data: newProfile,\n                    error: insertError\n                });\n                if (insertError) {\n                    console.error('Profile creation error:', insertError);\n                    throw new Error(`Failed to create profile: ${insertError.message}`);\n                }\n                setProfile(newProfile);\n            } else {\n                console.log('Single profile found');\n                setProfile(profiles[0]);\n            }\n        } catch (error) {\n            console.error('Detailed error:', error);\n            if (error instanceof Error) {\n                setError(`Error fetching profile: ${error.message}`);\n            } else {\n                setError('An unexpected error occurred while loading your profile');\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfileSettings.useEffect\": ()=>{\n            fetchProfile();\n        }\n    }[\"ProfileSettings.useEffect\"], []);\n    async function updateProfile(e) {\n        e.preventDefault();\n        if (!profile?.id) return;\n        setIsSaving(true);\n        setError(null);\n        setSuccessMessage(null);\n        try {\n            // Validate username length\n            if (profile.username && profile.username.length < 3) {\n                throw new Error('Username must be at least 3 characters long');\n            }\n            const { error } = await supabase.from('profiles').upsert({\n                ...profile,\n                updated_at: new Date().toISOString()\n            });\n            if (error) {\n                if (error.code === '23505') {\n                    throw new Error('Username is already taken');\n                }\n                throw error;\n            }\n            setSuccessMessage('Profile updated successfully');\n            router.refresh() // Refresh the page to update any displayed profile data\n            ;\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to update profile');\n        } finally{\n            setIsSaving(false);\n        }\n    }\n    async function handleAvatarUpload(event) {\n        try {\n            setError(null);\n            const file = event.target.files?.[0];\n            if (!file) return;\n            // Validate file type\n            if (!file.type.startsWith('image/')) {\n                setError('Please upload an image file');\n                return;\n            }\n            // Validate file size (5MB)\n            const maxSize = 5 * 1024 * 1024 // 5MB\n            ;\n            if (file.size > maxSize) {\n                setError('Image size must be less than 5MB');\n                return;\n            }\n            setIsSaving(true);\n            // Create a unique file name\n            const fileExt = file.name.split('.').pop();\n            const userId = profile?.id;\n            const fileName = `${userId}/${Date.now()}.${fileExt}`;\n            // Upload file\n            const { error: uploadError } = await supabase.storage.from('avatars').upload(fileName, file, {\n                cacheControl: '3600',\n                upsert: true\n            });\n            if (uploadError) throw uploadError;\n            // Get public URL\n            const { data: { publicUrl } } = supabase.storage.from('avatars').getPublicUrl(fileName);\n            // Update profile with new avatar URL\n            const { error: updateError } = await supabase.from('profiles').update({\n                avatar_url: publicUrl\n            }).eq('id', profile?.id);\n            if (updateError) throw updateError;\n            setProfile(profile ? {\n                ...profile,\n                avatar_url: publicUrl\n            } : null);\n            setSuccessMessage('Avatar updated successfully');\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to upload avatar');\n        } finally{\n            setIsSaving(false);\n        }\n    }\n    async function handleAvatarRemove() {\n        if (!profile?.avatar_url) return;\n        try {\n            setIsSaving(true);\n            setError(null);\n            // Extract file name from URL\n            const fileName = profile.avatar_url.split('/').pop();\n            if (fileName) {\n                // Remove file from storage\n                const { error: deleteError } = await supabase.storage.from('avatars').remove([\n                    fileName\n                ]);\n                if (deleteError) throw deleteError;\n            }\n            // Update profile\n            const { error: updateError } = await supabase.from('profiles').update({\n                avatar_url: null\n            }).eq('id', profile.id);\n            if (updateError) throw updateError;\n            setProfile(profile ? {\n                ...profile,\n                avatar_url: null\n            } : null);\n            setSuccessMessage('Avatar removed successfully');\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to remove avatar');\n        } finally{\n            setIsSaving(false);\n        }\n    }\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n            lineNumber: 259,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-3xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-white\",\n                        children: \"Profile Settings\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-white/60\",\n                        children: \"Manage your profile information and settings.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, this),\n            successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-green-500/10 border border-green-500/20 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-green-500\",\n                    children: successMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-[#111111] rounded-2xl p-8 border border-white/5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-8 pb-8 border-b border-white/5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 rounded-full bg-white/5 flex items-center justify-center overflow-hidden\",\n                                children: profile?.avatar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: profile.avatar_url,\n                                    alt: \"Profile\",\n                                    className: \"w-20 h-20 object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-12 h-12 text-white/20\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white\",\n                                        children: \"Profile Picture\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-white/60 mb-4\",\n                                        children: \"Upload a new profile picture or remove the current one\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        accept: \"image/*\",\n                                                        onChange: handleAvatarUpload,\n                                                        className: \"hidden\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-4 py-2 bg-white/5 hover:bg-white/10 text-white rounded-lg transition-colors inline-block\",\n                                                        children: \"Upload New\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this),\n                                            profile?.avatar_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: handleAvatarRemove,\n                                                className: \"px-4 py-2 bg-red-500/10 hover:bg-red-500/20 text-red-500 rounded-lg transition-colors\",\n                                                children: \"Remove\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: updateProfile,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"full_name\",\n                                            className: \"block text-sm font-medium text-white mb-2\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"full_name\",\n                                            value: profile?.full_name || '',\n                                            onChange: (e)=>setProfile(profile ? {\n                                                    ...profile,\n                                                    full_name: e.target.value\n                                                } : null),\n                                            className: \"w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500/20\",\n                                            placeholder: \"Enter your full name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"username\",\n                                            className: \"block text-sm font-medium text-white mb-2\",\n                                            children: \"Username\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"username\",\n                                            value: profile?.username || '',\n                                            onChange: (e)=>setProfile(profile ? {\n                                                    ...profile,\n                                                    username: e.target.value\n                                                } : null),\n                                            className: \"w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500/20\",\n                                            placeholder: \"Choose a username\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-white/60\",\n                                            children: \"This will be your public username visible to other users.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-white mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            id: \"email\",\n                                            value: profile?.email || '',\n                                            onChange: (e)=>setProfile(profile ? {\n                                                    ...profile,\n                                                    email: e.target.value\n                                                } : null),\n                                            className: \"w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500/20\",\n                                            placeholder: \"Enter your email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"website\",\n                                            className: \"block text-sm font-medium text-white mb-2\",\n                                            children: \"Website\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"url\",\n                                            id: \"website\",\n                                            value: profile?.website || '',\n                                            onChange: (e)=>setProfile(profile ? {\n                                                    ...profile,\n                                                    website: e.target.value\n                                                } : null),\n                                            className: \"w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500/20\",\n                                            placeholder: \"https://example.com\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSaving,\n                                        className: \"px-6 py-2 bg-green-500 hover:bg-green-600 text-black font-medium rounded-lg transition-colors disabled:opacity-50\",\n                                        children: isSaving ? 'Saving...' : 'Save Changes'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n        lineNumber: 266,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/profile/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/Header.tsx":
/*!*****************************************!*\
  !*** ./components/dashboard/Header.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Header() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const fetchUser = {\n                \"Header.useEffect.fetchUser\": async ()=>{\n                    try {\n                        const { data: { session } } = await supabase.auth.getSession();\n                        if (!session) {\n                            setUser(null);\n                            setIsLoading(false);\n                            return;\n                        }\n                        const { data: userData } = await supabase.from('users').select('id, email, credits').eq('id', session.user.id).single();\n                        if (userData) {\n                            setUser({\n                                id: userData.id,\n                                email: session.user.email || userData.email,\n                                credits: userData.credits\n                            });\n                        }\n                    } catch (error) {\n                        console.error('Error:', error);\n                        setError('Error loading user data');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"Header.useEffect.fetchUser\"];\n            fetchUser();\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"Header.useEffect\": async (_event, session)=>{\n                    if (session) {\n                        const { data: userData } = await supabase.from('users').select('id, email, credits').eq('id', session.user.id).single();\n                        if (userData) {\n                            setUser({\n                                id: userData.id,\n                                email: session.user.email || userData.email,\n                                credits: userData.credits\n                            });\n                        }\n                    } else {\n                        setUser(null);\n                        router.replace('/auth');\n                    }\n                }\n            }[\"Header.useEffect\"]);\n            return ({\n                \"Header.useEffect\": ()=>{\n                    subscription.unsubscribe();\n                }\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], [\n        supabase,\n        router\n    ]);\n    const handleSignOut = async ()=>{\n        try {\n            await supabase.auth.signOut();\n            router.replace('/auth');\n        } catch (error) {\n            console.error('Error signing out:', error);\n            setError('Error signing out');\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"fixed top-0 left-0 right-0 z-50 bg-[#0A0A0A] border-b border-white/10\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse bg-white/5 h-8 w-24 rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse bg-white/5 h-8 w-32 rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 left-0 right-0 z-50 bg-[#0A0A0A] border-b border-white/10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/\",\n                            className: \"text-white font-bold text-xl\",\n                            children: \"VG Kosh\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-500 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/60\",\n                                        children: [\n                                            \"Credits: \",\n                                            user?.credits || 0\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                                className: \"flex items-center space-x-2 text-white hover:text-white/80\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: user?.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: `w-5 h-5 transition-transform ${isMenuOpen ? 'rotate-180' : ''}`,\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M19 9l-7 7-7-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"py-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard\",\n                                                            className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                            onClick: ()=>setIsMenuOpen(false),\n                                                            children: \"Dashboard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/settings\",\n                                                            className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                            onClick: ()=>setIsMenuOpen(false),\n                                                            children: \"Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                setIsMenuOpen(false);\n                                                                handleSignOut();\n                                                            },\n                                                            className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                            children: \"Sign out\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/Header.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9be5cd341dcb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUHJpeWFkYXJzaGFuIFRpd2FyaVxcRG93bmxvYWRzXFxWRyBLb3NoXzEuMFxcVkcgS29zaF8xLjBcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5YmU1Y2QzNDFkY2JcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/layout.tsx":
/*!**********************************!*\
  !*** ./app/dashboard/layout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\VG Kosh_1.0\\VG Kosh_1.0\\app\\dashboard\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/dashboard/profile/page.tsx":
/*!****************************************!*\
  !*** ./app/dashboard/profile/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\VG Kosh_1.0\\VG Kosh_1.0\\app\\dashboard\\profile\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst metadata = {\n    title: 'VG Kosh - AI Document Management',\n    description: 'AI-powered document management with semantic search and GPT-4 chat integration'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} min-h-screen bg-primary text-secondary antialiased`,\n            suppressHydrationWarning: true,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzQjtBQUdoQkE7QUFFQyxNQUFNQyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0Msd0JBQXdCO2tCQUN0Qyw0RUFBQ0M7WUFBS0MsV0FBVyxHQUFHViwySkFBZSxDQUFDLG1EQUFtRCxDQUFDO1lBQUVRLHdCQUF3QjtzQkFDL0dIOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFByaXlhZGFyc2hhbiBUaXdhcmlcXERvd25sb2Fkc1xcVkcgS29zaF8xLjBcXFZHIEtvc2hfMS4wXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xyXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXHJcblxyXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXHJcblxyXG5leHBvcnQgY29uc3QgbWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6ICdWRyBLb3NoIC0gQUkgRG9jdW1lbnQgTWFuYWdlbWVudCcsXHJcbiAgZGVzY3JpcHRpb246ICdBSS1wb3dlcmVkIGRvY3VtZW50IG1hbmFnZW1lbnQgd2l0aCBzZW1hbnRpYyBzZWFyY2ggYW5kIEdQVC00IGNoYXQgaW50ZWdyYXRpb24nLFxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufToge1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aHRtbCBsYW5nPVwiZW5cIiBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc+XHJcbiAgICAgIDxib2R5IGNsYXNzTmFtZT17YCR7aW50ZXIuY2xhc3NOYW1lfSBtaW4taC1zY3JlZW4gYmctcHJpbWFyeSB0ZXh0LXNlY29uZGFyeSBhbnRpYWxpYXNlZGB9IHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cclxuICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvaHRtbD5cclxuICApXHJcbn1cclxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxQcml5YWRhcnNoYW4gVGl3YXJpXFxEb3dubG9hZHNcXFZHIEtvc2hfMS4wXFxWRyBLb3NoXzEuMFxcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@opentelemetry","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fprofile%2Fpage&page=%2Fdashboard%2Fprofile%2Fpage&appPaths=%2Fdashboard%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fprofile%2Fpage.tsx&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();