"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpenIcon,CodeBracketIcon,CpuChipIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpenIcon,CodeBracketIcon,CpuChipIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpenIcon,CodeBracketIcon,CpuChipIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpenIcon,CodeBracketIcon,CpuChipIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BookOpenIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpenIcon,CodeBracketIcon,CpuChipIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpenIcon,CodeBracketIcon,CpuChipIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpenIcon,CodeBracketIcon,CpuChipIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _components_PricingCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/PricingCard */ \"(app-pages-browser)/./app/components/PricingCard.tsx\");\n/* harmony import */ var _components_FaqItem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/FaqItem */ \"(app-pages-browser)/./app/components/FaqItem.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst features = [\n    {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"w-6 h-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n            lineNumber: 21,\n            columnNumber: 11\n        }, undefined),\n        title: 'Production-Ready Code',\n        description: 'Get started with professionally written, well-structured code that scales.'\n    },\n    {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"w-6 h-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n            lineNumber: 26,\n            columnNumber: 11\n        }, undefined),\n        title: 'Quick Deployment',\n        description: 'Deploy your application with one click to your favorite cloud platform.'\n    },\n    {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"w-6 h-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n            lineNumber: 31,\n            columnNumber: 11\n        }, undefined),\n        title: 'AI Integration',\n        description: 'Built-in AI capabilities to supercharge your application features.'\n    }\n];\nconst pricingPlans = [\n    {\n        title: 'Starter',\n        price: '99',\n        description: 'Perfect for side projects and small startups',\n        features: [\n            'Up to 5 team members',\n            'Basic analytics',\n            'Community support',\n            '5GB storage',\n            'API access'\n        ],\n        buttonText: 'Get Started',\n        priceId: 'price_1QTPalGI6vk81n8V8PtyW1ow'\n    },\n    {\n        title: 'Pro',\n        price: '249',\n        description: 'Best for growing businesses',\n        features: [\n            'Unlimited team members',\n            'Advanced analytics',\n            'Priority support',\n            '50GB storage',\n            'API access',\n            'Custom integrations'\n        ],\n        buttonText: 'Start Free Trial',\n        popular: true,\n        priceId: 'price_1QTPbgGI6vk81n8VgYFOi983'\n    },\n    {\n        title: 'Enterprise',\n        price: '999',\n        description: 'For large scale applications',\n        features: [\n            'Unlimited everything',\n            'White-label options',\n            '24/7 phone support',\n            '500GB storage',\n            'API access',\n            'Custom development'\n        ],\n        buttonText: 'Contact Sales',\n        priceId: 'price_1QTPcUGI6vk81n8V9567pzL9'\n    }\n];\nconst faqs = [\n    {\n        question: 'What is included in the starter package?',\n        answer: 'The starter package includes all essential features to get your project up and running, including basic analytics, community support, and API access.'\n    },\n    {\n        question: 'Can I upgrade my plan later?',\n        answer: 'Yes, you can upgrade your plan at any time. Your new features will be available immediately after upgrading.'\n    },\n    {\n        question: 'Do you offer custom development?',\n        answer: 'Yes, our enterprise plan includes custom development options to meet your specific needs.'\n    }\n];\nconst testimonials = [\n    {\n        content: \"This toolkit saved us months of development time. We launched our MVP in just 2 weeks!\",\n        author: {\n            name: \"Sarah Chen\",\n            avatar: \"/avatars/placeholder.svg\",\n            title: \"CTO at TechStart\"\n        },\n        stats: [\n            {\n                label: \"Time Saved\",\n                value: \"3 months\"\n            },\n            {\n                label: \"ROI\",\n                value: \"300%\"\n            }\n        ]\n    },\n    {\n        content: \"The code quality is exceptional. It's like having a senior developer on the team.\",\n        author: {\n            name: \"Mike Johnson\",\n            avatar: \"/avatars/placeholder.svg\",\n            title: \"Lead Developer\"\n        }\n    },\n    {\n        content: \"Best investment we made for our startup. The support is amazing too!\",\n        author: {\n            name: \"Lisa Park\",\n            avatar: \"/avatars/placeholder.svg\",\n            title: \"Founder at AppLabs\"\n        }\n    }\n];\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#0A0A0A]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full z-50 bg-[#0A0A0A]/80 backdrop-blur-sm border-b border-white/5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"text-xl font-bold text-white\",\n                                    children: \"VG Kosh\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/features\",\n                                        className: \"text-sm text-white/70 hover:text-white\",\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/pricing\",\n                                        className: \"text-sm text-white/70 hover:text-white\",\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/docs\",\n                                        className: \"text-sm text-white/70 hover:text-white flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Docs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"https://github.com/VG-2020dl/VG-Kosh/discussions\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"text-sm text-white/70 hover:text-white\",\n                                        children: \"Community\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/blog\",\n                                        className: \"text-sm text-white/70 hover:text-white\",\n                                        children: \"Blog\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"https://github.com/VG-2020dl/VG-Kosh\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"text-sm text-white/70 hover:text-white flex items-center space-x-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                clipRule: \"evenodd\",\n                                                d: \"M12 2C6.477 2 2 6.477 2 12c0 4.42 2.87 8.17 6.84 ********.66-.23.66-.5v-1.69c-2.77.6-3.36-1.34-3.36-1.34-.46-1.16-1.11-1.47-1.11-1.47-.91-.62.07-.6.07-.6 1 .07 1.53 1.03 1.53 1.03.87 1.52 2.34 1.07 **********-.65.35-1.09.63-1.34-2.22-.25-4.55-1.11-4.55-4.92 0-1.11.38-2 1.03-2.71-.1-.25-.45-1.29.1-2.64 0 0 .84-.27 2.75 1.02.79-.22 1.65-.33 2.5-.33.85 0 1.71.11 2.5.33 1.91-1.29 2.75-1.02 2.75-1.02.55 1.35.2 2.39.1 ********** 1.03 1.6 1.03 2.71 0 3.82-2.34 4.66-4.57 **********.69.92.69 1.85V21c0 .***********.5C19.14 20.16 22 16.42 22 12A10 10 0 0012 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth\",\n                                        className: \"text-sm text-white/70 hover:text-white\",\n                                        children: \"Sign in\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth?view=sign-up\",\n                                        className: \"bg-[#FFBE1A] text-black text-sm px-4 py-2 rounded-lg hover:bg-[#FFBE1A]/90\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-32 pb-16 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center px-3 py-1 rounded-full bg-white/5 border border-white/10 text-sm text-white/70 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-2\",\n                                                children: \"\\uD83E\\uDD16\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" AI-Powered Document Intelligence\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl sm:text-6xl font-bold text-white leading-[1.1] tracking-tight mb-6\",\n                                        children: [\n                                            \"AI-powered document\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 36\n                                            }, this),\n                                            \"management & chat\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-white/70 mb-8 leading-relaxed\",\n                                        children: \"Upload documents, search with AI, and chat with GPT-4 - all in one powerful platform\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-white/5 px-3 py-1 rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex\",\n                                                    children: '★★★★★'.split('').map((star, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-[#FFBE1A]\",\n                                                            children: star\n                                                        }, i, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-[#FFBE1A] font-medium\",\n                                                    children: \"4.9/5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-2 text-white/30\",\n                                                    children: \"•\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white/70\",\n                                                    children: \"from 1000+ reviews\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/auth?view=sign-up\",\n                                                className: \"inline-flex justify-center items-center px-6 py-3 rounded-lg bg-[#FFBE1A] text-black font-medium hover:bg-[#FFBE1A]/90 transition-colors\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"https://github.com/VG-2020dl/VG-Kosh\",\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"inline-flex justify-center items-center px-6 py-3 rounded-lg border border-white/10 text-white font-medium hover:bg-white/5 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            clipRule: \"evenodd\",\n                                                            d: \"M12 2C6.477 2 2 6.477 2 12c0 4.42 2.87 8.17 6.84 ********.66-.23.66-.5v-1.69c-2.77.6-3.36-1.34-3.36-1.34-.46-1.16-1.11-1.47-1.11-1.47-.91-.62.07-.6.07-.6 1 .07 1.53 1.03 1.53 1.03.87 1.52 2.34 1.07 **********-.65.35-1.09.63-1.34-2.22-.25-4.55-1.11-4.55-4.92 0-1.11.38-2 1.03-2.71-.1-.25-.45-1.29.1-2.64 0 0 .84-.27 2.75 1.02.79-.22 1.65-.33 2.5-.33.85 0 1.71.11 2.5.33 1.91-1.29 2.75-1.02 2.75-1.02.55 1.35.2 2.39.1 ********** 1.03 1.6 1.03 2.71 0 3.82-2.34 4.66-4.57 **********.69.92.69 1.85V21c0 .***********.5C19.14 20.16 22 16.42 22 12A10 10 0 0012 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Github Repo\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-[400px] lg:h-[500px]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: \"/Saas-Header.png\",\n                                    alt: \"SaaS Platform Preview\",\n                                    fill: true,\n                                    className: \"object-contain\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-[#4ADE80] text-sm font-mono\",\n                                children: 'const launch_time = \"01:19 AM\";'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-5xl font-bold mb-6 bg-gradient-to-r from-white/90 to-white/60 bg-clip-text text-transparent\",\n                                        children: [\n                                            \"Supercharge your app instantly,\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 48\n                                            }, this),\n                                            \"launch faster, make $\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-white/60 max-w-3xl\",\n                                        children: \"Upload PDFs, search through documents with AI, and chat with GPT-4 about your content. VG Kosh combines document management with artificial intelligence to transform how you work with information.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full bg-white/5 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"@\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: \"Emails\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full bg-white/5 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83D\\uDCB3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: \"Payments\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full bg-white/5 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83D\\uDC64\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: \"Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full bg-white/5 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83D\\uDDC4️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: \"Database\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full bg-white/5 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83D\\uDCC4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: \"SEO\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full bg-white/5 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83C\\uDFA8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: \"Style\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full bg-white/5 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"⋯\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FFBE1A] text-sm\",\n                                                children: \"More\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 text-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-[#4ADE80]\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/80\",\n                                                children: \"Tips to write copy that sells\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-[#4ADE80]\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/80\",\n                                                children: \"Discord community to stay accountable\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-[#4ADE80]\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/80\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#FFBE1A]\",\n                                                        children: \"Crisp\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" customer support (auto show/hide, variables...)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-[#4ADE80]\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/80\",\n                                                children: \"Collect emails for a waitlist if your product isn't ready\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-[#4ADE80]\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/80\",\n                                                children: \"Prompts to generate terms & privacy policy with ChatGPT\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-[#4ADE80]\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/80\",\n                                                children: \"Copy paste code templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-[#4ADE80]\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/80\",\n                                                children: \"Dead simple tutorials\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-[#4ADE80] text-lg\",\n                                children: \"Time saved: ∞ hours\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#1A1311] rounded-3xl p-10 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-[#FF6B6B] font-medium\",\n                                        children: [\n                                            \"4 hrs \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"to set up emails\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 65\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"+ 6 hrs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"designing a landing page\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 80\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"+ 4 hrs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"to handle Stripe webhooks\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 80\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"+ 2 hrs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"for SEO tags\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 80\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"+ 1 hr\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"applying for Google Oauth\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 79\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"+ 3 hrs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"for DNS records\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 80\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"+ 2 hrs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"for protected API routes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 80\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"+ ∞ hrs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"overthinking...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 80\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-3 flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#FF6B6B] font-medium\",\n                                                children: \"= 22+ hours\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"of headaches\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83C\\uDF27️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center text-white/60 gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"There's an easier way\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 380,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 bg-white/5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-white mb-8\",\n                            children: \"Watch Our Launch Announcement\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"aspect-video w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                className: \"w-full h-full rounded-2xl shadow-2xl\",\n                                src: \"https://www.youtube.com/embed/JiXy9JGkGzo\",\n                                title: \"Best SAAS Kit Pro Launch Announcement\",\n                                allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n                                allowFullScreen: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-white text-center mb-12\",\n                            children: [\n                                \"Save hours of repetitive code,\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 43\n                                }, this),\n                                \"ship fast, get profitable\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: pricingPlans.map((plan, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PricingCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    ...plan\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 429,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white mb-4\",\n                                    children: \"Get in Touch\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-white/60 max-w-2xl mx-auto\",\n                                    children: \"Have questions about our product? Need help getting started? We're here to help.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-white mb-4\",\n                                                    children: \"Contact Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white/60 mb-6\",\n                                                    children: \"Fill out the form and we'll get back to you within 24 hours.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-white/5 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-6 h-6 text-[#FFBE1A]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-white font-medium mb-1\",\n                                                                    children: \"Email\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"mailto:<EMAIL>\",\n                                                                    className: \"text-white/60 hover:text-[#FFBE1A]\",\n                                                                    children: \"<EMAIL>\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-white/5 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-6 h-6 text-[#FFBE1A]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-white font-medium mb-1\",\n                                                                    children: \"Phone\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"tel:+1234567890\",\n                                                                    className: \"text-white/60 hover:text-[#FFBE1A]\",\n                                                                    children: \"+1 (234) 567-890\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-white/5 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpenIcon_CodeBracketIcon_CpuChipIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-6 h-6 text-[#FFBE1A]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-white font-medium mb-1\",\n                                                                    children: \"Office\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white/60\",\n                                                                    children: [\n                                                                        \"123 Innovation Street\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 44\n                                                                        }, this),\n                                                                        \"San Francisco, CA 94107\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-white font-medium mb-4\",\n                                                    children: \"Follow Us\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#FFBE1A]\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#FFBE1A]\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#FFBE1A]\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm5.894 8.221l-1.97 9.28c-.145.658-.537.818-1.084.508l-3-2.21-1.446 1.394c-.14.18-.357.223-.548.223l.188-2.85 5.18-4.68c.223-.204-.054-.31-.346-.106l-6.4 4.02-2.76-.92c-.6-.183-.612-.6.125-.89l10.782-4.156c.5-.183.94.114.78.89z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#FFBE1A]\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M12 0C5.374 0 0 5.373 0 12c0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/[0.02] rounded-2xl p-8 border border-white/5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"first_name\",\n                                                                className: \"block text-sm font-medium text-white mb-2\",\n                                                                children: \"First Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"first_name\",\n                                                                className: \"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]/50 focus:border-[#FFBE1A] text-white placeholder-white/40\",\n                                                                placeholder: \"John\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"last_name\",\n                                                                className: \"block text-sm font-medium text-white mb-2\",\n                                                                children: \"Last Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"last_name\",\n                                                                className: \"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]/50 focus:border-[#FFBE1A] text-white placeholder-white/40\",\n                                                                placeholder: \"Doe\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"email\",\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        id: \"email\",\n                                                        className: \"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]/50 focus:border-[#FFBE1A] text-white placeholder-white/40\",\n                                                        placeholder: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"subject\",\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Subject\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"subject\",\n                                                        className: \"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]/50 focus:border-[#FFBE1A] text-white placeholder-white/40\",\n                                                        placeholder: \"How can we help?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"message\",\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Message\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"message\",\n                                                        rows: 4,\n                                                        className: \"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]/50 focus:border-[#FFBE1A] text-white placeholder-white/40\",\n                                                        placeholder: \"Your message...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"w-full px-6 py-3 bg-[#FFBE1A] text-black font-medium rounded-lg hover:bg-[#FFBE1A]/90 transition-colors\",\n                                                children: \"Send Message\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 443,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-3xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-white text-center mb-12\",\n                            children: \"Frequently Asked Questions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: faqs.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FaqItem__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    ...faq\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 612,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 608,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 607,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-24 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl font-bold text-white mb-4\",\n                                children: [\n                                    \"5000+ makers built AI tools,\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 43\n                                    }, this),\n                                    \"SaaS, and more\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 623,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-[#111111] rounded-xl p-6 border border-white/5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center text-white font-medium\",\n                                                    children: \"SC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: \"Sarah Chen\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"CTO at TechStart\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 mb-6\",\n                                            children: \"This toolkit saved us months of development time. We launched our MVP in just 2 weeks!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"3 months\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 647,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"Time Saved\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-[#4ADE80]\",\n                                                            children: \"300%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"ROI\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-[#111111] rounded-xl p-6 border border-white/5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center text-white font-medium\",\n                                                    children: \"MJ\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: \"Mike Johnson\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"Lead Developer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 mb-6\",\n                                            children: \"The code quality is exceptional. It's like having a senior developer on the team.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"50+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"Components\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-[#4ADE80]\",\n                                                            children: \"100%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"TypeScript\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 678,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-[#111111] rounded-xl p-6 border border-white/5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 rounded-full bg-gradient-to-br from-amber-500 to-orange-500 flex items-center justify-center text-white font-medium\",\n                                                    children: \"LP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: \"Lisa Park\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"Founder at AppLabs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 mb-6\",\n                                            children: \"Best investment we made for our startup. The support is amazing too!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"24/7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"Support\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-[#4ADE80]\",\n                                                            children: \"95%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white/60\",\n                                                            children: \"Satisfaction\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 702,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-16 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center bg-white/5 px-4 py-2 rounded-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex -space-x-2 mr-3\",\n                                        children: [\n                                            ...Array(5)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-full border-2 border-[#111111] bg-gradient-to-br from-purple-500 to-pink-500\",\n                                                style: {\n                                                    transform: \"translateX(\".concat(i * -4, \"px)\")\n                                                }\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/60 text-sm\",\n                                        children: [\n                                            \"Join \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-medium\",\n                                                children: \"5,000+\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" makers\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 711,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 622,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 621,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"py-20 px-4 border-t border-white/5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-4\",\n                                        children: \"Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/features\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Features\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/pricing\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Pricing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/docs\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Documentation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 739,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/changelog\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Changelog\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 740,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 736,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-4\",\n                                        children: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/about\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"About\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/blog\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Blog\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 747,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/careers\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Careers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/contact\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Contact\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 749,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 745,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-4\",\n                                        children: \"Resources\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"https://github.com/VG-2020dl/VG-Kosh/discussions\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Community\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/help\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Help Center\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/status\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/terms\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Terms of Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 765,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 752,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-white mb-4\",\n                                        children: \"Connect\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 769,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"https://twitter.com\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Twitter\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"https://github.com/VG-2020dl/VG-Kosh\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"GitHub\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"https://discord.com\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Discord\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 773,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/newsletter\",\n                                                    className: \"text-white/70 hover:text-white\",\n                                                    children: \"Newsletter\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 768,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 733,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                    lineNumber: 732,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n                lineNumber: 731,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\page.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});