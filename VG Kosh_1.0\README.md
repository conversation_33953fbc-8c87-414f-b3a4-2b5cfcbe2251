# VG Kosh 🚀

A modern, production-ready AI-powered SaaS application built with Next.js 15, TypeScript, Tailwind CSS, and more. Features document management, semantic search, and GPT-4 chat integration.

![SaaS Kit Banner](public/Saas-Header.png)

## 🌟 Features

- ⚡ **Next.js 15** with App Router & Server Actions
- 🎨 **Tailwind CSS 3.3** for beautiful UI
- 📱 **Fully Responsive** design
- 🔐 **Authentication** with Supabase
- 💳 **Stripe Integration** for payments
- 📧 **Email Integration** with Resend
- 🤖 **GPT-4 Chat Interface** with OpenAI
- 📄 **Document Management** with PDF upload
- 🔍 **Semantic Search** with Pinecone vector database
- 🎯 **SEO Optimized**
- 🌙 **Dark Mode** ready
- 🔍 **Type Safe** with TypeScript 5.3

## 🚀 Getting Started

### Prerequisites

Before you begin, ensure you have the following installed:
- [Node.js](https://nodejs.org/) (version 18.17 or higher)
- [Git](https://git-scm.com/)
- [npm](https://www.npmjs.com/) (comes with Node.js)

### Step-by-Step Installation Guide

1. **Clone the Repository**
   ```bash
   git clone https://github.com/VG-2020dl/VG-Kosh.git
   ```

2. **Navigate to Project Directory**
   ```bash
   cd VG-Kosh
   ```

3. **Install Dependencies**
   ```bash
   npm install
   ```

4. **Set Up Environment Variables**
   - Copy the example environment file:
     ```bash
     cp .env.example .env.local
     ```
   - Open `.env.local` and fill in your environment variables:
     ```env
     # App
     NEXT_PUBLIC_APP_URL=http://localhost:3000

     # Supabase
     NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
     NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
     SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

     # Stripe
     STRIPE_SECRET_KEY=your_stripe_secret_key
     STRIPE_WEBHOOK_SECRET=your_webhook_secret
     NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_publishable_key

     # OpenAI
     OPENAI_API_KEY=your_openai_api_key

     # Pinecone
     PINECONE_API_KEY=your_pinecone_api_key
     PINECONE_INDEX_NAME=vg-kosh-documents

     # Email (Resend)
     RESEND_API_KEY=your_resend_api_key
     ```

5. **Run Development Server**
   ```bash
   npm run dev
   ```

6. **Open Your Browser**
   Visit [http://localhost:3000](http://localhost:3000) to see your application running.

## 🔧 Configuration Guide

### Setting Up Supabase

1. Create a [Supabase](https://supabase.com/) account
2. Create a new project
3. Go to Project Settings > API
4. Copy the URL and anon key to your `.env.local`

### Setting Up Stripe

1. Create a [Stripe](https://stripe.com/) account
2. Get your API keys from the Stripe Dashboard
3. Add them to your `.env.local`
4. Set up webhook endpoints (detailed in documentation)

### Setting Up OpenAI

1. Create an [OpenAI](https://platform.openai.com/) account
2. Get your API key from the API keys section
3. Add it to your `.env.local`

### Setting Up Pinecone

1. Create a [Pinecone](https://app.pinecone.io/) account
2. Create a new index with 1536 dimensions
3. Get your API key and add it to your `.env.local`

### Setting Up Email with Resend

1. Create a [Resend](https://resend.com/) account
2. Get your API key
3. Add it to your `.env.local`

## 📚 Project Structure

```
VG-Kosh/
├── app/                # Next.js app router pages
├── components/         # React components
├── lib/               # Utility functions
├── utils/             # Utility functions
├── public/            # Static assets
├── supabase/          # Database migrations
├── src/scripts/       # Python scripts for bulk operations
└── ...config files
```

## 🛠️ Development Tools

- **Code Quality**
  - ESLint for code linting
  - Prettier for code formatting
  - TypeScript for type safety

- **Git Hooks**
  - Husky for Git hooks
  - lint-staged for staged files linting

## 🤝 Contributing

We welcome contributions! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/)
- [Tailwind CSS](https://tailwindcss.com/)
- [shadcn/ui](https://ui.shadcn.com/)
- [Supabase](https://supabase.com/)
- [Stripe](https://stripe.com/)
- [Resend](https://resend.com/)

## 💬 Support

If you have any questions or need help, please open an issue or contact <NAME_EMAIL>

---

Built with ❤️ by [VG-2020dl](https://github.com/VG-2020dl)
