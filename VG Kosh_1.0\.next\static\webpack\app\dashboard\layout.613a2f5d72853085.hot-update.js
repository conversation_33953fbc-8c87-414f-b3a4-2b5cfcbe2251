"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/@supabase/auth-helpers-nextjs/dist/index.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  createBrowserSupabaseClient: () => createBrowserSupabaseClient,\n  createClientComponentClient: () => createClientComponentClient,\n  createMiddlewareClient: () => createMiddlewareClient,\n  createMiddlewareSupabaseClient: () => createMiddlewareSupabaseClient,\n  createPagesBrowserClient: () => createPagesBrowserClient,\n  createPagesServerClient: () => createPagesServerClient,\n  createRouteHandlerClient: () => createRouteHandlerClient,\n  createServerActionClient: () => createServerActionClient,\n  createServerComponentClient: () => createServerComponentClient,\n  createServerSupabaseClient: () => createServerSupabaseClient\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/clientComponentClient.ts\nvar import_auth_helpers_shared = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar supabase;\nfunction createClientComponentClient({\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions,\n  isSingleton = true\n} = {}) {\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      \"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\"\n    );\n  }\n  const createNewClient = () => {\n    var _a;\n    return (0, import_auth_helpers_shared.createSupabaseClient)(supabaseUrl, supabaseKey, {\n      ...options,\n      global: {\n        ...options == null ? void 0 : options.global,\n        headers: {\n          ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n          \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.10.0\"}`\n        }\n      },\n      auth: {\n        storage: new import_auth_helpers_shared.BrowserCookieAuthStorageAdapter(cookieOptions)\n      }\n    });\n  };\n  if (isSingleton) {\n    const _supabase = supabase ?? createNewClient();\n    if (typeof window === \"undefined\")\n      return _supabase;\n    if (!supabase)\n      supabase = _supabase;\n    return supabase;\n  }\n  return createNewClient();\n}\n\n// src/pagesBrowserClient.ts\nvar createPagesBrowserClient = createClientComponentClient;\n\n// src/pagesServerClient.ts\nvar import_auth_helpers_shared2 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar import_set_cookie_parser = __webpack_require__(/*! set-cookie-parser */ \"(app-pages-browser)/./node_modules/set-cookie-parser/lib/set-cookie.js\");\nvar NextServerAuthStorageAdapter = class extends import_auth_helpers_shared2.CookieAuthStorageAdapter {\n  constructor(context, cookieOptions) {\n    super(cookieOptions);\n    this.context = context;\n  }\n  getCookie(name) {\n    var _a, _b, _c;\n    const setCookie = (0, import_set_cookie_parser.splitCookiesString)(\n      ((_b = (_a = this.context.res) == null ? void 0 : _a.getHeader(\"set-cookie\")) == null ? void 0 : _b.toString()) ?? \"\"\n    ).map((c) => (0, import_auth_helpers_shared2.parseCookies)(c)[name]).find((c) => !!c);\n    const value = setCookie ?? ((_c = this.context.req) == null ? void 0 : _c.cookies[name]);\n    return value;\n  }\n  setCookie(name, value) {\n    this._setCookie(name, value);\n  }\n  deleteCookie(name) {\n    this._setCookie(name, \"\", {\n      maxAge: 0\n    });\n  }\n  _setCookie(name, value, options) {\n    var _a;\n    const setCookies = (0, import_set_cookie_parser.splitCookiesString)(\n      ((_a = this.context.res.getHeader(\"set-cookie\")) == null ? void 0 : _a.toString()) ?? \"\"\n    ).filter((c) => !(name in (0, import_auth_helpers_shared2.parseCookies)(c)));\n    const cookieStr = (0, import_auth_helpers_shared2.serializeCookie)(name, value, {\n      ...this.cookieOptions,\n      ...options,\n      // Allow supabase-js on the client to read the cookie as well\n      httpOnly: false\n    });\n    this.context.res.setHeader(\"set-cookie\", [...setCookies, cookieStr]);\n  }\n};\nfunction createPagesServerClient(context, {\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions\n} = {}) {\n  var _a;\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      \"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\"\n    );\n  }\n  return (0, import_auth_helpers_shared2.createSupabaseClient)(supabaseUrl, supabaseKey, {\n    ...options,\n    global: {\n      ...options == null ? void 0 : options.global,\n      headers: {\n        ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n        \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.10.0\"}`\n      }\n    },\n    auth: {\n      storage: new NextServerAuthStorageAdapter(context, cookieOptions)\n    }\n  });\n}\n\n// src/middlewareClient.ts\nvar import_auth_helpers_shared3 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar import_set_cookie_parser2 = __webpack_require__(/*! set-cookie-parser */ \"(app-pages-browser)/./node_modules/set-cookie-parser/lib/set-cookie.js\");\nvar NextMiddlewareAuthStorageAdapter = class extends import_auth_helpers_shared3.CookieAuthStorageAdapter {\n  constructor(context, cookieOptions) {\n    super(cookieOptions);\n    this.context = context;\n  }\n  getCookie(name) {\n    var _a;\n    const setCookie = (0, import_set_cookie_parser2.splitCookiesString)(\n      ((_a = this.context.res.headers.get(\"set-cookie\")) == null ? void 0 : _a.toString()) ?? \"\"\n    ).map((c) => (0, import_auth_helpers_shared3.parseCookies)(c)[name]).find((c) => !!c);\n    if (setCookie) {\n      return setCookie;\n    }\n    const cookies = (0, import_auth_helpers_shared3.parseCookies)(this.context.req.headers.get(\"cookie\") ?? \"\");\n    return cookies[name];\n  }\n  setCookie(name, value) {\n    this._setCookie(name, value);\n  }\n  deleteCookie(name) {\n    this._setCookie(name, \"\", {\n      maxAge: 0\n    });\n  }\n  _setCookie(name, value, options) {\n    const newSessionStr = (0, import_auth_helpers_shared3.serializeCookie)(name, value, {\n      ...this.cookieOptions,\n      ...options,\n      // Allow supabase-js on the client to read the cookie as well\n      httpOnly: false\n    });\n    if (this.context.res.headers) {\n      this.context.res.headers.append(\"set-cookie\", newSessionStr);\n    }\n  }\n};\nfunction createMiddlewareClient(context, {\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions\n} = {}) {\n  var _a;\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      \"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\"\n    );\n  }\n  return (0, import_auth_helpers_shared3.createSupabaseClient)(supabaseUrl, supabaseKey, {\n    ...options,\n    global: {\n      ...options == null ? void 0 : options.global,\n      headers: {\n        ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n        \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.10.0\"}`\n      }\n    },\n    auth: {\n      storage: new NextMiddlewareAuthStorageAdapter(context, cookieOptions)\n    }\n  });\n}\n\n// src/serverComponentClient.ts\nvar import_auth_helpers_shared4 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar NextServerComponentAuthStorageAdapter = class extends import_auth_helpers_shared4.CookieAuthStorageAdapter {\n  constructor(context, cookieOptions) {\n    super(cookieOptions);\n    this.context = context;\n    this.isServer = true;\n  }\n  getCookie(name) {\n    var _a;\n    const nextCookies = this.context.cookies();\n    return (_a = nextCookies.get(name)) == null ? void 0 : _a.value;\n  }\n  setCookie(name, value) {\n  }\n  deleteCookie(name) {\n  }\n};\nfunction createServerComponentClient(context, {\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions\n} = {}) {\n  var _a;\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      \"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\"\n    );\n  }\n  return (0, import_auth_helpers_shared4.createSupabaseClient)(supabaseUrl, supabaseKey, {\n    ...options,\n    global: {\n      ...options == null ? void 0 : options.global,\n      headers: {\n        ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n        \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.10.0\"}`\n      }\n    },\n    auth: {\n      storage: new NextServerComponentAuthStorageAdapter(context, cookieOptions)\n    }\n  });\n}\n\n// src/routeHandlerClient.ts\nvar import_auth_helpers_shared5 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar NextRouteHandlerAuthStorageAdapter = class extends import_auth_helpers_shared5.CookieAuthStorageAdapter {\n  constructor(context, cookieOptions) {\n    super(cookieOptions);\n    this.context = context;\n  }\n  getCookie(name) {\n    var _a;\n    const nextCookies = this.context.cookies();\n    return (_a = nextCookies.get(name)) == null ? void 0 : _a.value;\n  }\n  setCookie(name, value) {\n    const nextCookies = this.context.cookies();\n    nextCookies.set(name, value, this.cookieOptions);\n  }\n  deleteCookie(name) {\n    const nextCookies = this.context.cookies();\n    nextCookies.set(name, \"\", {\n      ...this.cookieOptions,\n      maxAge: 0\n    });\n  }\n};\nfunction createRouteHandlerClient(context, {\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions\n} = {}) {\n  var _a;\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      \"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\"\n    );\n  }\n  return (0, import_auth_helpers_shared5.createSupabaseClient)(supabaseUrl, supabaseKey, {\n    ...options,\n    global: {\n      ...options == null ? void 0 : options.global,\n      headers: {\n        ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n        \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.10.0\"}`\n      }\n    },\n    auth: {\n      storage: new NextRouteHandlerAuthStorageAdapter(context, cookieOptions)\n    }\n  });\n}\n\n// src/serverActionClient.ts\nvar createServerActionClient = createRouteHandlerClient;\n\n// src/deprecated.ts\nfunction createBrowserSupabaseClient({\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions\n} = {}) {\n  console.warn(\n    \"Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages\"\n  );\n  return createPagesBrowserClient({\n    supabaseUrl,\n    supabaseKey,\n    options,\n    cookieOptions\n  });\n}\nfunction createServerSupabaseClient(context, {\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions\n} = {}) {\n  console.warn(\n    \"Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages\"\n  );\n  return createPagesServerClient(context, {\n    supabaseUrl,\n    supabaseKey,\n    options,\n    cookieOptions\n  });\n}\nfunction createMiddlewareSupabaseClient(context, {\n  supabaseUrl = \"https://xifuwecoszfaelnqvejx.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU\",\n  options,\n  cookieOptions\n} = {}) {\n  console.warn(\n    \"Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware\"\n  );\n  return createMiddlewareClient(context, {\n    supabaseUrl,\n    supabaseKey,\n    options,\n    cookieOptions\n  });\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\n"));

/***/ })

});