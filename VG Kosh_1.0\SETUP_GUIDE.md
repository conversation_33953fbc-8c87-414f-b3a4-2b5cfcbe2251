# 🚀 VG Kosh - Complete Setup Guide

## ✅ **COMPLETED STEPS**

### 1. Repository Ownership ✅
- **Repository**: https://github.com/VG-2020dl/VG-Kosh
- **Branding**: Updated to "VG Kosh"
- **Git Remote**: Configured to your repository

### 2. Supabase Configuration ✅
- **URL**: `https://xifuwecoszfaelnqvejx.supabase.co`
- **Anon Key**: ✅ **Updated with your key**
- **Status**: ✅ **Ready for authentication**

---

## 🔧 **NEXT STEPS TO COMPLETE SETUP**

### 3. Complete Supabase Setup

#### **A. Get Your Service Role Key**
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project: `xifuwecoszfaelnqvejx`
3. Go to **Settings** → **API**
4. Copy the **service_role** key (NOT the anon key)
5. Add it to your `.env.local`:
   ```bash
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
   ```

#### **B. Get Your Database Password**
1. In Supabase Dashboard → **Settings** → **Database**
2. Copy your database password
3. Update the POSTGRES_URL in `.env.local`:
   ```bash
   POSTGRES_URL="postgresql://postgres:<EMAIL>:5432/postgres"
   ```

#### **C. Set Up Database Tables**
The application needs these tables. Run this SQL in Supabase SQL Editor:

```sql
-- Enable RLS
ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

-- Create users table
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  credits INTEGER DEFAULT 100,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create customer_subscriptions table
CREATE TABLE public.customer_subscriptions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  subscription_id TEXT NOT NULL,
  status TEXT NOT NULL,
  price_id TEXT NOT NULL,
  quantity INTEGER DEFAULT 1,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  cancel_at TIMESTAMP WITH TIME ZONE,
  canceled_at TIMESTAMP WITH TIME ZONE,
  current_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
  current_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
  created TIMESTAMP WITH TIME ZONE NOT NULL,
  ended_at TIMESTAMP WITH TIME ZONE,
  trial_start TIMESTAMP WITH TIME ZONE,
  trial_end TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create documents table for Pinecone integration
CREATE TABLE public.documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  filename TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  pinecone_ids TEXT[] DEFAULT '{}',
  status TEXT DEFAULT 'processing'
);

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view own data" ON public.users
  FOR ALL USING (auth.uid() = id);

CREATE POLICY "Users can view own subscriptions" ON public.customer_subscriptions
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own documents" ON public.documents
  FOR ALL USING (auth.uid() = user_id);

-- Create function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, credits)
  VALUES (NEW.id, NEW.email, 100);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

### 4. Set Up OpenAI (for GPT-4 Chat)

1. Go to [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create a new API key
3. Add it to `.env.local`:
   ```bash
   OPENAI_API_KEY=sk-your-openai-api-key-here
   ```

### 5. Set Up Pinecone (for Document Search)

1. Go to [Pinecone Console](https://app.pinecone.io/)
2. Create a new index:
   - **Name**: `vg-kosh-documents`
   - **Dimensions**: `1536`
   - **Metric**: `cosine`
3. Get your API key from the dashboard
4. Add to `.env.local`:
   ```bash
   PINECONE_API_KEY=your-pinecone-api-key-here
   ```

### 6. Set Up Stripe (for Payments)

1. Go to [Stripe Dashboard](https://dashboard.stripe.com/)
2. Get your API keys from **Developers** → **API keys**
3. Create products and prices for your subscription plans
4. Add to `.env.local`:
   ```bash
   STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-publishable-key
   ```
5. Update price IDs in `app/dashboard/billing/page.tsx`

### 7. Set Up Resend (for Emails)

1. Go to [Resend Dashboard](https://resend.com/dashboard)
2. Create an API key
3. Add to `.env.local`:
   ```bash
   RESEND_API_KEY=re_your-resend-api-key
   ```

---

## 🧪 **TESTING YOUR SETUP**

### Test Authentication
1. Go to http://localhost:3000/auth
2. Try signing up with your email
3. Check if user is created in Supabase

### Test Chat (after OpenAI setup)
1. Sign in and go to http://localhost:3000/dashboard
2. Try sending a message in the chat interface

### Test Document Upload (after Pinecone setup)
1. Go to http://localhost:3000/dashboard/documents
2. Try uploading a PDF file

---

## 📋 **CURRENT STATUS**

| Service | Status | Action Needed |
|---------|--------|---------------|
| **Repository** | ✅ **Complete** | None |
| **Branding** | ✅ **Complete** | None |
| **Supabase URL & Anon Key** | ✅ **Complete** | None |
| **Supabase Service Role** | ⏳ **Pending** | Add service role key |
| **Database Tables** | ⏳ **Pending** | Run SQL setup script |
| **OpenAI** | ⏳ **Pending** | Add API key |
| **Pinecone** | ⏳ **Pending** | Add API key |
| **Stripe** | ⏳ **Pending** | Add API keys & price IDs |
| **Resend** | ⏳ **Pending** | Add API key |

---

## 🎯 **PRIORITY ORDER**

1. **HIGH**: Complete Supabase setup (service role + database)
2. **MEDIUM**: Add OpenAI API key (for chat functionality)
3. **MEDIUM**: Add Pinecone API key (for document search)
4. **LOW**: Add Stripe keys (for payments)
5. **LOW**: Add Resend key (for emails)

Your VG Kosh application is now properly configured with your Supabase credentials and ready for the next steps!
