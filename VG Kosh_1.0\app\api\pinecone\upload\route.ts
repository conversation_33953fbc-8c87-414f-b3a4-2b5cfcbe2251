import { NextRequest, NextResponse } from 'next/server';
import { uploadDocument } from '@/lib/pinecone';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import pdf from 'pdf-parse';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const title = formData.get('title') as string;
    const description = formData.get('description') as string;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    if (!file.name.toLowerCase().endsWith('.pdf')) {
      return NextResponse.json({ error: 'Only PDF files are supported' }, { status: 400 });
    }

    // Convert file to buffer and extract text
    const buffer = Buffer.from(await file.arrayBuffer());
    const pdfData = await pdf(buffer);
    const text = pdfData.text;

    if (!text || text.trim().length === 0) {
      return NextResponse.json({ error: 'No text found in PDF' }, { status: 400 });
    }

    // Generate unique document ID
    const documentId = `${session.user.id}-${Date.now()}-${file.name.replace(/[^a-zA-Z0-9]/g, '-')}`;

    // Prepare metadata
    const metadata = {
      title: title || file.name,
      description: description || '',
      filename: file.name,
      fileSize: file.size,
      userId: session.user.id,
      uploadedAt: new Date().toISOString(),
    };

    // Upload to Pinecone
    const result = await uploadDocument(documentId, text, metadata);

    // Store document info in Supabase
    const { error: dbError } = await supabase
      .from('documents')
      .insert({
        id: documentId,
        user_id: session.user.id,
        title: metadata.title,
        description: metadata.description,
        filename: file.name,
        file_size: file.size,
        chunks_count: result.chunksUploaded,
        status: 'uploaded',
        created_at: new Date().toISOString(),
      });

    if (dbError) {
      console.error('Database error:', dbError);
      // Continue anyway, as the document is already in Pinecone
    }

    return NextResponse.json({
      success: true,
      documentId: result.documentId,
      chunksUploaded: result.chunksUploaded,
      message: 'Document uploaded successfully',
    });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'Failed to upload document' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's documents from Supabase
    const { data: documents, error } = await supabase
      .from('documents')
      .select('*')
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: 'Failed to fetch documents' }, { status: 500 });
    }

    return NextResponse.json({ documents });

  } catch (error) {
    console.error('Fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch documents' },
      { status: 500 }
    );
  }
}
