# Supabase Configuration (VG-2020dl's project)
NEXT_PUBLIC_SUPABASE_URL=https://xifuwecoszfaelnqvejx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNjg0NjgsImV4cCI6MjA2NTY0NDQ2OH0.cKcGAWQL1eUK6YbjyFO8iE_qMtiHlv1EQuCPS6TvrUU
POSTGRES_URL="****************************************/postgres"

# Service Role Key (VG-2020dl's project)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhpZnV3ZWNvc3pmYWVsbnF2ZWp4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA2ODQ2OCwiZXhwIjoyMDY1NjQ0NDY4fQ.KqoYCpxXPOlDjYDZ87oZ4tsloqiz14FIbV8rFoKTStk

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Stripe Configuration (you'll need to add these)
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=

# OpenAI Configuration (you'll need to add this)
OPENAI_API_KEY=

# Pinecone Configuration (you'll need to add these)
PINECONE_API_KEY=
PINECONE_INDEX_NAME=vg-kosh-documents

# Resend Configuration (you'll need to add this)
RESEND_API_KEY=

# Python Script Configuration (for bulk PDF uploads)
PDF_FOLDER_PATH=./uploads
START_BATCH=0
BATCH_SIZE=50
