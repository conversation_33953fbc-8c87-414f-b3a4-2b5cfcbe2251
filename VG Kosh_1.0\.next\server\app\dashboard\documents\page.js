/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/documents/page";
exports.ids = ["app/dashboard/documents/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fdocuments%2Fpage&page=%2Fdashboard%2Fdocuments%2Fpage&appPaths=%2Fdashboard%2Fdocuments%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fdocuments%2Fpage.tsx&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fdocuments%2Fpage&page=%2Fdashboard%2Fdocuments%2Fpage&appPaths=%2Fdashboard%2Fdocuments%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fdocuments%2Fpage.tsx&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/layout.tsx */ \"(rsc)/./app/dashboard/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/documents/page.tsx */ \"(rsc)/./app/dashboard/documents/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'documents',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\documents\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\documents\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/documents/page\",\n        pathname: \"/dashboard/documents\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fdocuments%2Fpage&page=%2Fdashboard%2Fdocuments%2Fpage&appPaths=%2Fdashboard%2Fdocuments%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fdocuments%2Fpage.tsx&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/layout.tsx */ \"(rsc)/./app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1ByaXlhZGFyc2hhbiUyMFRpd2FyaSU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1ZHJTIwS29zaF8xLjAlNUMlNUNWRyUyMEtvc2hfMS4wJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQW9JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxQcml5YWRhcnNoYW4gVGl3YXJpXFxcXERvd25sb2Fkc1xcXFxWRyBLb3NoXzEuMFxcXFxWRyBLb3NoXzEuMFxcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/layout.tsx */ \"(ssr)/./app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1ByaXlhZGFyc2hhbiUyMFRpd2FyaSU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1ZHJTIwS29zaF8xLjAlNUMlNUNWRyUyMEtvc2hfMS4wJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQW9JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxQcml5YWRhcnNoYW4gVGl3YXJpXFxcXERvd25sb2Fkc1xcXFxWRyBLb3NoXzEuMFxcXFxWRyBLb3NoXzEuMFxcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Ccomponents%5C%5Cdashboard%5C%5CDocumentManager.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Ccomponents%5C%5Cdashboard%5C%5CDocumentManager.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/dashboard/DocumentManager.tsx */ \"(rsc)/./components/dashboard/DocumentManager.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1ByaXlhZGFyc2hhbiUyMFRpd2FyaSU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1ZHJTIwS29zaF8xLjAlNUMlNUNWRyUyMEtvc2hfMS4wJTVDJTVDY29tcG9uZW50cyU1QyU1Q2Rhc2hib2FyZCU1QyU1Q0RvY3VtZW50TWFuYWdlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBaUwiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxQcml5YWRhcnNoYW4gVGl3YXJpXFxcXERvd25sb2Fkc1xcXFxWRyBLb3NoXzEuMFxcXFxWRyBLb3NoXzEuMFxcXFxjb21wb25lbnRzXFxcXGRhc2hib2FyZFxcXFxEb2N1bWVudE1hbmFnZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Ccomponents%5C%5Cdashboard%5C%5CDocumentManager.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Ccomponents%5C%5Cdashboard%5C%5CDocumentManager.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Ccomponents%5C%5Cdashboard%5C%5CDocumentManager.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/dashboard/DocumentManager.tsx */ \"(ssr)/./components/dashboard/DocumentManager.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1ByaXlhZGFyc2hhbiUyMFRpd2FyaSU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1ZHJTIwS29zaF8xLjAlNUMlNUNWRyUyMEtvc2hfMS4wJTVDJTVDY29tcG9uZW50cyU1QyU1Q2Rhc2hib2FyZCU1QyU1Q0RvY3VtZW50TWFuYWdlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBaUwiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxQcml5YWRhcnNoYW4gVGl3YXJpXFxcXERvd25sb2Fkc1xcXFxWRyBLb3NoXzEuMFxcXFxWRyBLb3NoXzEuMFxcXFxjb21wb25lbnRzXFxcXGRhc2hib2FyZFxcXFxEb2N1bWVudE1hbmFnZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Ccomponents%5C%5Cdashboard%5C%5CDocumentManager.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPriyadarshan%20Tiwari%5C%5CDownloads%5C%5CVG%20Kosh_1.0%5C%5CVG%20Kosh_1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/layout.tsx":
/*!**********************************!*\
  !*** ./app/dashboard/layout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _components_dashboard_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/Header */ \"(ssr)/./components/dashboard/Header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst navigation = [\n    {\n        name: 'Overview',\n        href: '/dashboard',\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'Analytics',\n        href: '/dashboard/analytics',\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'Profile',\n        href: '/dashboard/profile',\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'Documents',\n        href: '/dashboard/documents',\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'Settings',\n        href: '/dashboard/settings',\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    }\n];\nfunction DashboardLayout({ children }) {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__.createClientComponentClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardLayout.useEffect\": ()=>{\n            const checkSession = {\n                \"DashboardLayout.useEffect.checkSession\": async ()=>{\n                    try {\n                        const { data: { session } } = await supabase.auth.getSession();\n                        if (!session) {\n                            router.replace('/auth');\n                            return;\n                        }\n                        setIsAuthenticated(true);\n                    } catch (error) {\n                        console.error('Error checking session:', error);\n                        router.replace('/auth');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"DashboardLayout.useEffect.checkSession\"];\n            checkSession();\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"DashboardLayout.useEffect\": (_event, session)=>{\n                    if (!session) {\n                        router.replace('/auth');\n                        setIsAuthenticated(false);\n                    } else {\n                        setIsAuthenticated(true);\n                    }\n                    setIsLoading(false);\n                }\n            }[\"DashboardLayout.useEffect\"]);\n            return ({\n                \"DashboardLayout.useEffect\": ()=>{\n                    subscription.unsubscribe();\n                }\n            })[\"DashboardLayout.useEffect\"];\n        }\n    }[\"DashboardLayout.useEffect\"], [\n        router,\n        supabase.auth\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-[calc(100vh-4rem)] pt-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed left-0 w-64 h-[calc(100vh-4rem)] bg-[#111111] border-r border-white/5 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"p-4 space-y-1\",\n                            children: navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: item.href,\n                                    className: `\n                    flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors\n                    ${isActive ? 'bg-white/10 text-white' : 'text-white/60 hover:bg-white/5 hover:text-white'}\n                  `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"w-5 h-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-64 flex-1 overflow-y-auto p-4 sm:p-6 lg:p-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/DocumentManager.tsx":
/*!**************************************************!*\
  !*** ./components/dashboard/DocumentManager.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_DocumentIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentIcon.js\");\n/* harmony import */ var _DocumentUpload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DocumentUpload */ \"(ssr)/./components/dashboard/DocumentUpload.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DocumentManager() {\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searching, setSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUpload, setShowUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notification, setNotification] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentManager.useEffect\": ()=>{\n            fetchDocuments();\n        }\n    }[\"DocumentManager.useEffect\"], []);\n    const fetchDocuments = async ()=>{\n        try {\n            const response = await fetch('/api/pinecone/upload');\n            if (response.ok) {\n                const data = await response.json();\n                setDocuments(data.documents || []);\n            }\n        } catch (error) {\n            console.error('Error fetching documents:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSearch = async ()=>{\n        if (!searchQuery.trim()) {\n            setSearchResults([]);\n            return;\n        }\n        setSearching(true);\n        try {\n            const response = await fetch('/api/pinecone/search', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: searchQuery,\n                    topK: 10\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setSearchResults(data.results || []);\n            }\n        } catch (error) {\n            console.error('Error searching documents:', error);\n            showNotification('error', 'Failed to search documents');\n        } finally{\n            setSearching(false);\n        }\n    };\n    const handleUploadSuccess = (result)=>{\n        showNotification('success', `Document uploaded successfully! ${result.chunksUploaded} chunks processed.`);\n        fetchDocuments();\n        setShowUpload(false);\n    };\n    const handleUploadError = (error)=>{\n        showNotification('error', `Upload failed: ${error}`);\n    };\n    const showNotification = (type, message)=>{\n        setNotification({\n            type,\n            message\n        });\n        setTimeout(()=>setNotification(null), 5000);\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            notification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `p-4 rounded-md ${notification.type === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`,\n                children: notification.message\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Document Manager\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowUpload(!showUpload),\n                        className: \"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\",\n                        children: showUpload ? 'Cancel' : 'Upload Document'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            showUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onUploadSuccess: handleUploadSuccess,\n                onUploadError: handleUploadError\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Search Documents\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                    onKeyPress: (e)=>e.key === 'Enter' && handleSearch(),\n                                    placeholder: \"Search through your documents...\",\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSearch,\n                                disabled: searching || !searchQuery.trim(),\n                                className: \"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: searching ? 'Searching...' : 'Search'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    searchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-md font-medium text-gray-900 mb-3\",\n                                children: \"Search Results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: searchResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: result.document?.title || 'Unknown Document'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Score: \",\n                                                            (result.score * 100).toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 line-clamp-3\",\n                                                children: result.text\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: [\n                                                    \"Chunk \",\n                                                    result.chunkIndex + 1,\n                                                    \" from \",\n                                                    result.document?.filename\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Your Documents\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    documents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No documents uploaded yet.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"Upload your first PDF to get started!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: documents.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 hover:bg-gray-50 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-8 w-8 text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: doc.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: doc.filename\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        doc.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600 mt-1\",\n                                                            children: doc.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: formatFileSize(doc.file_size)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        doc.chunks_count,\n                                                        \" chunks\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: formatDate(doc.created_at)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 17\n                                }, this)\n                            }, doc.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/DocumentManager.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/DocumentUpload.tsx":
/*!*************************************************!*\
  !*** ./components/dashboard/DocumentUpload.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(ssr)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowUpIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowUpIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowUpIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowUpIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DocumentUpload({ onUploadSuccess, onUploadError }) {\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[onDrop]\": (acceptedFiles)=>{\n            if (acceptedFiles.length > 0) {\n                const file = acceptedFiles[0];\n                setSelectedFile(file);\n                setTitle(file.name.replace('.pdf', ''));\n            }\n        }\n    }[\"DocumentUpload.useCallback[onDrop]\"], []);\n    const { getRootProps, getInputProps, isDragActive } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: {\n            'application/pdf': [\n                '.pdf'\n            ]\n        },\n        maxFiles: 1,\n        disabled: uploading\n    });\n    const handleUpload = async ()=>{\n        if (!selectedFile) return;\n        setUploading(true);\n        setUploadProgress(0);\n        try {\n            const formData = new FormData();\n            formData.append('file', selectedFile);\n            formData.append('title', title);\n            formData.append('description', description);\n            // Simulate progress for better UX\n            const progressInterval = setInterval(()=>{\n                setUploadProgress((prev)=>Math.min(prev + 10, 90));\n            }, 200);\n            const response = await fetch('/api/pinecone/upload', {\n                method: 'POST',\n                body: formData\n            });\n            clearInterval(progressInterval);\n            setUploadProgress(100);\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || 'Upload failed');\n            }\n            const result = await response.json();\n            // Reset form\n            setSelectedFile(null);\n            setTitle('');\n            setDescription('');\n            setUploadProgress(0);\n            onUploadSuccess?.(result);\n        } catch (error) {\n            console.error('Upload error:', error);\n            onUploadError?.(error instanceof Error ? error.message : 'Upload failed');\n            setUploadProgress(0);\n        } finally{\n            setUploading(false);\n        }\n    };\n    const removeFile = ()=>{\n        setSelectedFile(null);\n        setTitle('');\n        setDescription('');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                children: \"Upload Document\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            !selectedFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ...getRootProps(),\n                className: `border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${isDragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ...getInputProps()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowUpIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 mb-2\",\n                        children: isDragActive ? 'Drop the PDF file here...' : 'Drag & drop a PDF file here, or click to select'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"Only PDF files are supported\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowUpIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: selectedFile.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    (selectedFile.size / 1024 / 1024).toFixed(2),\n                                                    \" MB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: removeFile,\n                                disabled: uploading,\n                                className: \"text-gray-400 hover:text-gray-600 disabled:opacity-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowUpIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"title\",\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Title\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"title\",\n                                value: title,\n                                onChange: (e)=>setTitle(e.target.value),\n                                disabled: uploading,\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100\",\n                                placeholder: \"Enter document title\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"description\",\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Description (Optional)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"description\",\n                                value: description,\n                                onChange: (e)=>setDescription(e.target.value),\n                                disabled: uploading,\n                                rows: 3,\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100\",\n                                placeholder: \"Enter document description\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-gray-600 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Uploading...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            uploadProgress,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                    style: {\n                                        width: `${uploadProgress}%`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleUpload,\n                        disabled: uploading || !title.trim(),\n                        className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                        children: uploading ? 'Uploading...' : 'Upload Document'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentUpload.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/DocumentUpload.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/Header.tsx":
/*!*****************************************!*\
  !*** ./components/dashboard/Header.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Header() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const fetchUser = {\n                \"Header.useEffect.fetchUser\": async ()=>{\n                    try {\n                        const { data: { session } } = await supabase.auth.getSession();\n                        if (!session) {\n                            setUser(null);\n                            setIsLoading(false);\n                            return;\n                        }\n                        const { data: userData } = await supabase.from('users').select('id, email, credits').eq('id', session.user.id).single();\n                        if (userData) {\n                            setUser({\n                                id: userData.id,\n                                email: session.user.email || userData.email,\n                                credits: userData.credits\n                            });\n                        }\n                    } catch (error) {\n                        console.error('Error:', error);\n                        setError('Error loading user data');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"Header.useEffect.fetchUser\"];\n            fetchUser();\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"Header.useEffect\": async (_event, session)=>{\n                    if (session) {\n                        const { data: userData } = await supabase.from('users').select('id, email, credits').eq('id', session.user.id).single();\n                        if (userData) {\n                            setUser({\n                                id: userData.id,\n                                email: session.user.email || userData.email,\n                                credits: userData.credits\n                            });\n                        }\n                    } else {\n                        setUser(null);\n                        router.replace('/auth');\n                    }\n                }\n            }[\"Header.useEffect\"]);\n            return ({\n                \"Header.useEffect\": ()=>{\n                    subscription.unsubscribe();\n                }\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], [\n        supabase,\n        router\n    ]);\n    const handleSignOut = async ()=>{\n        try {\n            await supabase.auth.signOut();\n            router.replace('/auth');\n        } catch (error) {\n            console.error('Error signing out:', error);\n            setError('Error signing out');\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"fixed top-0 left-0 right-0 z-50 bg-[#0A0A0A] border-b border-white/10\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse bg-white/5 h-8 w-24 rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse bg-white/5 h-8 w-32 rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 left-0 right-0 z-50 bg-[#0A0A0A] border-b border-white/10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/\",\n                            className: \"text-white font-bold text-xl\",\n                            children: \"SAAS Kit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-500 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/60\",\n                                        children: [\n                                            \"Credits: \",\n                                            user?.credits || 0\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                                className: \"flex items-center space-x-2 text-white hover:text-white/80\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: user?.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: `w-5 h-5 transition-transform ${isMenuOpen ? 'rotate-180' : ''}`,\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M19 9l-7 7-7-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"py-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard\",\n                                                            className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                            onClick: ()=>setIsMenuOpen(false),\n                                                            children: \"Dashboard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/settings\",\n                                                            className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                            onClick: ()=>setIsMenuOpen(false),\n                                                            children: \"Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                setIsMenuOpen(false);\n                                                                handleSignOut();\n                                                            },\n                                                            className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                            children: \"Sign out\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\Header.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/Header.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9be5cd341dcb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUHJpeWFkYXJzaGFuIFRpd2FyaVxcRG93bmxvYWRzXFxWRyBLb3NoXzEuMFxcVkcgS29zaF8xLjBcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5YmU1Y2QzNDFkY2JcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/documents/page.tsx":
/*!******************************************!*\
  !*** ./app/dashboard/documents/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_dashboard_DocumentManager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/dashboard/DocumentManager */ \"(rsc)/./components/dashboard/DocumentManager.tsx\");\n\n\nfunction DocumentsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DocumentManager__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\documents\\\\page.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\documents\\\\page.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZGFzaGJvYXJkL2RvY3VtZW50cy9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFxRTtBQUV0RCxTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0gsNkVBQWVBOzs7Ozs7Ozs7O0FBR3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFByaXlhZGFyc2hhbiBUaXdhcmlcXERvd25sb2Fkc1xcVkcgS29zaF8xLjBcXFZHIEtvc2hfMS4wXFxhcHBcXGRhc2hib2FyZFxcZG9jdW1lbnRzXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgRG9jdW1lbnRNYW5hZ2VyIGZyb20gJ0AvY29tcG9uZW50cy9kYXNoYm9hcmQvRG9jdW1lbnRNYW5hZ2VyJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnRzUGFnZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktOFwiPlxuICAgICAgPERvY3VtZW50TWFuYWdlciAvPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkRvY3VtZW50TWFuYWdlciIsIkRvY3VtZW50c1BhZ2UiLCJkaXYiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/dashboard/documents/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/layout.tsx":
/*!**********************************!*\
  !*** ./app/dashboard/layout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\VG Kosh_1.0\\VG Kosh_1.0\\app\\dashboard\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst metadata = {\n    title: 'Best SAAS Kit Pro',\n    description: 'The ultimate starter kit for your SAAS project'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} min-h-screen bg-primary text-secondary antialiased`,\n            suppressHydrationWarning: true,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzQjtBQUdoQkE7QUFFQyxNQUFNQyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0Msd0JBQXdCO2tCQUN0Qyw0RUFBQ0M7WUFBS0MsV0FBVyxHQUFHViwySkFBZSxDQUFDLG1EQUFtRCxDQUFDO1lBQUVRLHdCQUF3QjtzQkFDL0dIOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFByaXlhZGFyc2hhbiBUaXdhcmlcXERvd25sb2Fkc1xcVkcgS29zaF8xLjBcXFZHIEtvc2hfMS4wXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xyXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXHJcblxyXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXHJcblxyXG5leHBvcnQgY29uc3QgbWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6ICdCZXN0IFNBQVMgS2l0IFBybycsXHJcbiAgZGVzY3JpcHRpb246ICdUaGUgdWx0aW1hdGUgc3RhcnRlciBraXQgZm9yIHlvdXIgU0FBUyBwcm9qZWN0JyxcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXHJcbn0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGh0bWwgbGFuZz1cImVuXCIgc3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nPlxyXG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2Ake2ludGVyLmNsYXNzTmFtZX0gbWluLWgtc2NyZWVuIGJnLXByaW1hcnkgdGV4dC1zZWNvbmRhcnkgYW50aWFsaWFzZWRgfSBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc+XHJcbiAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/dashboard/DocumentManager.tsx":
/*!**************************************************!*\
  !*** ./components/dashboard/DocumentManager.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\components\\\\dashboard\\\\DocumentManager.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\VG Kosh_1.0\\VG Kosh_1.0\\components\\dashboard\\DocumentManager.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxQcml5YWRhcnNoYW4gVGl3YXJpXFxEb3dubG9hZHNcXFZHIEtvc2hfMS4wXFxWRyBLb3NoXzEuMFxcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@opentelemetry","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/@heroicons","vendor-chunks/prop-types","vendor-chunks/react-is","vendor-chunks/object-assign","vendor-chunks/file-selector","vendor-chunks/react-dropzone","vendor-chunks/tslib","vendor-chunks/attr-accept"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fdocuments%2Fpage&page=%2Fdashboard%2Fdocuments%2Fpage&appPaths=%2Fdashboard%2Fdocuments%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fdocuments%2Fpage.tsx&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();