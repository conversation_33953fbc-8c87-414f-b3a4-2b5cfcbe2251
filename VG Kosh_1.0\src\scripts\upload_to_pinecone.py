import os
import pdfplumber
from langchain.text_splitter import RecursiveCharacterTextSplitter
from openai import OpenAI
from pinecone import Pinecone
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# ----------------------
# Configuration Section
# ----------------------
openai_api_key = os.getenv("OPENAI_API_KEY")
pinecone_api_key = os.getenv("PINECONE_API_KEY")
index_name = os.getenv("PINECONE_INDEX_NAME", "vg-kosh-documents")
pdf_folder = os.getenv("PDF_FOLDER_PATH", "./uploads")

# Validate required environment variables
if not openai_api_key:
    raise ValueError("OPENAI_API_KEY environment variable is required")
if not pinecone_api_key:
    raise ValueError("PINECONE_API_KEY environment variable is required")

# ----------------------
# Resume Support - Set the Batch Number to Resume From
start_batch = int(os.getenv("START_BATCH", "0"))
batch_size = int(os.getenv("BATCH_SIZE", "50"))

# ----------------------
# Initialize Clients
client = OpenAI(api_key=openai_api_key)
pc = Pinecone(api_key=pinecone_api_key)
index = pc.Index(index_name)

# ----------------------
# Safe PDF Extraction Function
def extract_text(file_path):
    try:
        text = ""
        with pdfplumber.open(file_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        return text
    except Exception as e:
        print(f"⚠️ Failed to process {file_path}: {e}")
        return None

# ----------------------
# Chunk Preparation
def process_pdfs(folder_path):
    splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
    all_chunks = []
    
    if not os.path.exists(folder_path):
        print(f"❌ PDF folder not found: {folder_path}")
        return all_chunks
    
    for filename in os.listdir(folder_path):
        if filename.endswith(".pdf"):
            file_path = os.path.join(folder_path, filename)
            print(f"Processing {filename}...")
            text = extract_text(file_path)
            if not text:
                continue
            splits = splitter.split_text(text)
            for i, chunk in enumerate(splits):
                all_chunks.append({
                    "id": f"{filename}-{i}",
                    "text": chunk,
                    "metadata": {
                        "source": filename,
                        "chunk_index": i,
                        "upload_timestamp": int(time.time())
                    }
                })
    
    return all_chunks

# ----------------------
# Embedding Generation with Retry Logic
def get_embedding(text, retries=3, delay=5):
    for attempt in range(retries):
        try:
            response = client.embeddings.create(
                model="text-embedding-ada-002",
                input=[text]
            )
            return response.data[0].embedding
        except Exception as e:
            print(f"⚠️ Embedding failed (attempt {attempt + 1}): {e}")
            if attempt < retries - 1:
                print(f"Retrying in {delay} seconds...")
                time.sleep(delay)
            else:
                print("❌ Skipping this chunk after multiple failed attempts.")
                return None

# ----------------------
# Upload to Pinecone in Batches with Resume Support
def upload_to_pinecone(chunks):
    total_batches = len(chunks) // batch_size + (1 if len(chunks) % batch_size > 0 else 0)
    
    for i in range(start_batch * batch_size, len(chunks), batch_size):
        batch = chunks[i:i + batch_size]
        current_batch_num = i // batch_size + 1
        print(f"Uploading batch {current_batch_num} of {total_batches}")
        
        vectors = []
        for item in batch:
            embedding = get_embedding(item["text"])
            if embedding is None:
                continue
            vectors.append({
                "id": item["id"],
                "values": embedding,
                "metadata": item["metadata"]
            })
        
        if vectors:
            try:
                index.upsert(vectors=vectors)
                print(f"✅ Uploaded {len(vectors)} vectors.")
            except Exception as e:
                print(f"❌ Failed to upload batch {current_batch_num}: {e}")
                print(f"❗ Stopping script. Resume by setting START_BATCH={current_batch_num}")
                break
        else:
            print("⚠️ No vectors in this batch to upload.")
        
        time.sleep(1)  # Prevent rate limit issues

def main():
    print("🚀 Starting VG Kosh PDF Upload to Pinecone")
    print(f"📁 Processing PDFs from: {pdf_folder}")
    print(f"🎯 Target Pinecone index: {index_name}")
    
    # Process PDFs
    chunks = process_pdfs(pdf_folder)
    
    if not chunks:
        print("❌ No PDF chunks found to process.")
        return
    
    print(f"📊 Total chunks to process: {len(chunks)}")
    
    # Upload to Pinecone
    upload_to_pinecone(chunks)
    
    print("✅ VG Kosh PDF upload completed.")

if __name__ == "__main__":
    main()
