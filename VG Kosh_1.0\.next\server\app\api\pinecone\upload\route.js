/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/pinecone/upload/route";
exports.ids = ["app/api/pinecone/upload/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/pdf-parse/lib/pdf.js sync recursive ^\\.\\/.*\\/build\\/pdf\\.js$":
/*!**************************************************************************!*\
  !*** ./node_modules/pdf-parse/lib/pdf.js/ sync ^\.\/.*\/build\/pdf\.js$ ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./v1.10.100/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.10.100/build/pdf.js",
	"./v1.10.88/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.10.88/build/pdf.js",
	"./v1.9.426/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.9.426/build/pdf.js",
	"./v2.0.550/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v2.0.550/build/pdf.js"
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = "(rsc)/./node_modules/pdf-parse/lib/pdf.js sync recursive ^\\.\\/.*\\/build\\/pdf\\.js$";

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream/web");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpinecone%2Fupload%2Froute&page=%2Fapi%2Fpinecone%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpinecone%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpinecone%2Fupload%2Froute&page=%2Fapi%2Fpinecone%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpinecone%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Priyadarshan_Tiwari_Downloads_VG_Kosh_1_0_VG_Kosh_1_0_app_api_pinecone_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/pinecone/upload/route.ts */ \"(rsc)/./app/api/pinecone/upload/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/pinecone/upload/route\",\n        pathname: \"/api/pinecone/upload\",\n        filename: \"route\",\n        bundlePath: \"app/api/pinecone/upload/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\VG Kosh_1.0\\\\VG Kosh_1.0\\\\app\\\\api\\\\pinecone\\\\upload\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Priyadarshan_Tiwari_Downloads_VG_Kosh_1_0_VG_Kosh_1_0_app_api_pinecone_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpinecone%2Fupload%2Froute&page=%2Fapi%2Fpinecone%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpinecone%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/api/pinecone/upload/route.ts":
/*!******************************************!*\
  !*** ./app/api/pinecone/upload/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_pinecone__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/pinecone */ \"(rsc)/./lib/pinecone.ts\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var pdf_parse__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! pdf-parse */ \"(rsc)/./node_modules/pdf-parse/index.js\");\n/* harmony import */ var pdf_parse__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(pdf_parse__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nasync function POST(request) {\n    try {\n        // Check authentication\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createRouteHandlerClient)({\n            cookies: next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies\n        });\n        const { data: { session } } = await supabase.auth.getSession();\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const formData = await request.formData();\n        const file = formData.get('file');\n        const title = formData.get('title');\n        const description = formData.get('description');\n        if (!file) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No file provided'\n            }, {\n                status: 400\n            });\n        }\n        if (!file.name.toLowerCase().endsWith('.pdf')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Only PDF files are supported'\n            }, {\n                status: 400\n            });\n        }\n        // Convert file to buffer and extract text\n        const buffer = Buffer.from(await file.arrayBuffer());\n        const pdfData = await pdf_parse__WEBPACK_IMPORTED_MODULE_4___default()(buffer);\n        const text = pdfData.text;\n        if (!text || text.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No text found in PDF'\n            }, {\n                status: 400\n            });\n        }\n        // Generate unique document ID\n        const documentId = `${session.user.id}-${Date.now()}-${file.name.replace(/[^a-zA-Z0-9]/g, '-')}`;\n        // Prepare metadata\n        const metadata = {\n            title: title || file.name,\n            description: description || '',\n            filename: file.name,\n            fileSize: file.size,\n            userId: session.user.id,\n            uploadedAt: new Date().toISOString()\n        };\n        // Upload to Pinecone\n        const result = await (0,_lib_pinecone__WEBPACK_IMPORTED_MODULE_1__.uploadDocument)(documentId, text, metadata);\n        // Store document info in Supabase\n        const { error: dbError } = await supabase.from('documents').insert({\n            id: documentId,\n            user_id: session.user.id,\n            title: metadata.title,\n            description: metadata.description,\n            filename: file.name,\n            file_size: file.size,\n            chunks_count: result.chunksUploaded,\n            status: 'uploaded',\n            created_at: new Date().toISOString()\n        });\n        if (dbError) {\n            console.error('Database error:', dbError);\n        // Continue anyway, as the document is already in Pinecone\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            documentId: result.documentId,\n            chunksUploaded: result.chunksUploaded,\n            message: 'Document uploaded successfully'\n        });\n    } catch (error) {\n        console.error('Upload error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to upload document'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        // Check authentication\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createRouteHandlerClient)({\n            cookies: next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies\n        });\n        const { data: { session } } = await supabase.auth.getSession();\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Get user's documents from Supabase\n        const { data: documents, error } = await supabase.from('documents').select('*').eq('user_id', session.user.id).order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Database error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch documents'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            documents\n        });\n    } catch (error) {\n        console.error('Fetch error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch documents'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/pinecone/upload/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/pinecone.ts":
/*!*************************!*\
  !*** ./lib/pinecone.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteDocument: () => (/* binding */ deleteDocument),\n/* harmony export */   generateEmbedding: () => (/* binding */ generateEmbedding),\n/* harmony export */   getIndex: () => (/* binding */ getIndex),\n/* harmony export */   getIndexStats: () => (/* binding */ getIndexStats),\n/* harmony export */   searchDocuments: () => (/* binding */ searchDocuments),\n/* harmony export */   splitText: () => (/* binding */ splitText),\n/* harmony export */   uploadDocument: () => (/* binding */ uploadDocument)\n/* harmony export */ });\n/* harmony import */ var _pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @pinecone-database/pinecone */ \"(rsc)/./node_modules/@pinecone-database/pinecone/dist/index.js\");\n/* harmony import */ var _pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n/* harmony import */ var langchain_text_splitter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! langchain/text_splitter */ \"(rsc)/./node_modules/langchain/text_splitter.js\");\n\n\n\n// Initialize Pinecone client\nconst pinecone = new _pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0__.Pinecone({\n    apiKey: process.env.PINECONE_API_KEY\n});\n// Initialize OpenAI client\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_2__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\n// Get the Pinecone index\nconst getIndex = ()=>{\n    const indexName = process.env.PINECONE_INDEX_NAME || 'vg-kosh-documents';\n    return pinecone.index(indexName);\n};\n// Generate embeddings using OpenAI\nasync function generateEmbedding(text) {\n    try {\n        const response = await openai.embeddings.create({\n            model: 'text-embedding-ada-002',\n            input: text\n        });\n        return response.data[0].embedding;\n    } catch (error) {\n        console.error('Error generating embedding:', error);\n        throw new Error('Failed to generate embedding');\n    }\n}\n// Split text into chunks\nfunction splitText(text, chunkSize = 500, chunkOverlap = 50) {\n    const splitter = new langchain_text_splitter__WEBPACK_IMPORTED_MODULE_1__.RecursiveCharacterTextSplitter({\n        chunkSize,\n        chunkOverlap\n    });\n    return splitter.splitText(text);\n}\n// Upload document chunks to Pinecone\nasync function uploadDocument(documentId, text, metadata = {}) {\n    try {\n        const index = getIndex();\n        const chunks = splitText(text);\n        const vectors = [];\n        for(let i = 0; i < chunks.length; i++){\n            const chunk = chunks[i];\n            const embedding = await generateEmbedding(chunk);\n            vectors.push({\n                id: `${documentId}-chunk-${i}`,\n                values: embedding,\n                metadata: {\n                    ...metadata,\n                    text: chunk,\n                    chunkIndex: i,\n                    documentId,\n                    uploadTimestamp: Date.now()\n                }\n            });\n        }\n        // Upload in batches to avoid rate limits\n        const batchSize = 50;\n        for(let i = 0; i < vectors.length; i += batchSize){\n            const batch = vectors.slice(i, i + batchSize);\n            await index.upsert(batch);\n            // Small delay to avoid rate limits\n            if (i + batchSize < vectors.length) {\n                await new Promise((resolve)=>setTimeout(resolve, 100));\n            }\n        }\n        return {\n            success: true,\n            chunksUploaded: vectors.length,\n            documentId\n        };\n    } catch (error) {\n        console.error('Error uploading document:', error);\n        throw new Error('Failed to upload document to Pinecone');\n    }\n}\n// Search for similar documents\nasync function searchDocuments(query, topK = 5, filter) {\n    try {\n        const index = getIndex();\n        const queryEmbedding = await generateEmbedding(query);\n        const searchResponse = await index.query({\n            vector: queryEmbedding,\n            topK,\n            includeMetadata: true,\n            filter\n        });\n        return searchResponse.matches?.map((match)=>({\n                id: match.id,\n                score: match.score,\n                text: match.metadata?.text,\n                documentId: match.metadata?.documentId,\n                chunkIndex: match.metadata?.chunkIndex,\n                metadata: match.metadata\n            })) || [];\n    } catch (error) {\n        console.error('Error searching documents:', error);\n        throw new Error('Failed to search documents');\n    }\n}\n// Delete document from Pinecone\nasync function deleteDocument(documentId) {\n    try {\n        const index = getIndex();\n        // First, find all chunks for this document\n        const searchResponse = await index.query({\n            vector: new Array(1536).fill(0),\n            topK: 10000,\n            includeMetadata: true,\n            filter: {\n                documentId\n            }\n        });\n        if (searchResponse.matches && searchResponse.matches.length > 0) {\n            const chunkIds = searchResponse.matches.map((match)=>match.id);\n            await index.deleteMany(chunkIds);\n        }\n        return {\n            success: true,\n            deletedChunks: searchResponse.matches?.length || 0\n        };\n    } catch (error) {\n        console.error('Error deleting document:', error);\n        throw new Error('Failed to delete document from Pinecone');\n    }\n}\n// Get document statistics\nasync function getIndexStats() {\n    try {\n        const index = getIndex();\n        const stats = await index.describeIndexStats();\n        return stats;\n    } catch (error) {\n        console.error('Error getting index stats:', error);\n        throw new Error('Failed to get index statistics');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/pinecone.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/pdf-parse","vendor-chunks/formdata-node","vendor-chunks/openai","vendor-chunks/web-streams-polyfill","vendor-chunks/node-fetch","vendor-chunks/event-target-shim","vendor-chunks/eventemitter3","vendor-chunks/agentkeepalive","vendor-chunks/form-data-encoder","vendor-chunks/abort-controller","vendor-chunks/ms","vendor-chunks/humanize-ms","vendor-chunks/@pinecone-database","vendor-chunks/zod","vendor-chunks/@langchain","vendor-chunks/semver","vendor-chunks/zod-to-json-schema","vendor-chunks/langsmith","vendor-chunks/@cfworker","vendor-chunks/uuid","vendor-chunks/retry","vendor-chunks/p-queue","vendor-chunks/langchain","vendor-chunks/js-tiktoken","vendor-chunks/p-timeout","vendor-chunks/p-retry","vendor-chunks/p-finally","vendor-chunks/decamelize","vendor-chunks/camelcase","vendor-chunks/base64-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpinecone%2Fupload%2Froute&page=%2Fapi%2Fpinecone%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpinecone%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPriyadarshan%20Tiwari%5CDownloads%5CVG%20Kosh_1.0%5CVG%20Kosh_1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();