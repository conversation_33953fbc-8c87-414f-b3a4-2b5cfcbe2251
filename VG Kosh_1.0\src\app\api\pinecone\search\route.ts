import { NextRequest, NextResponse } from 'next/server';
import { searchDocuments } from '@/lib/pinecone';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { query, topK = 5, documentIds } = body;

    if (!query || typeof query !== 'string') {
      return NextResponse.json({ error: 'Query is required' }, { status: 400 });
    }

    // Prepare filter to only search user's documents
    let filter: Record<string, any> = {
      userId: session.user.id,
    };

    // If specific document IDs are provided, filter by them
    if (documentIds && Array.isArray(documentIds) && documentIds.length > 0) {
      filter.documentId = { $in: documentIds };
    }

    // Search in Pinecone
    const results = await searchDocuments(query, topK, filter);

    // Get document metadata from Supabase for additional context
    const documentIds_found = [...new Set(results.map(r => r.documentId))];
    
    let documentsMetadata = {};
    if (documentIds_found.length > 0) {
      const { data: docs } = await supabase
        .from('documents')
        .select('id, title, filename, description')
        .in('id', documentIds_found);
      
      if (docs) {
        documentsMetadata = docs.reduce((acc, doc) => {
          acc[doc.id] = doc;
          return acc;
        }, {} as Record<string, any>);
      }
    }

    // Enhance results with document metadata
    const enhancedResults = results.map(result => ({
      ...result,
      document: documentsMetadata[result.documentId] || null,
    }));

    return NextResponse.json({
      success: true,
      query,
      results: enhancedResults,
      totalResults: results.length,
    });

  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json(
      { error: 'Failed to search documents' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const topK = parseInt(searchParams.get('topK') || '5');
    const documentId = searchParams.get('documentId');

    if (!query) {
      return NextResponse.json({ error: 'Query parameter "q" is required' }, { status: 400 });
    }

    // Prepare filter
    let filter: Record<string, any> = {
      userId: session.user.id,
    };

    if (documentId) {
      filter.documentId = documentId;
    }

    // Search in Pinecone
    const results = await searchDocuments(query, topK, filter);

    return NextResponse.json({
      success: true,
      query,
      results,
      totalResults: results.length,
    });

  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json(
      { error: 'Failed to search documents' },
      { status: 500 }
    );
  }
}
